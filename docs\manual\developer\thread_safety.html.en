<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>Apache 2.0 Thread Safety Issues - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="./"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a> &gt; <a href="./">Developer Documentation</a></div><div id="page-content"><div id="preamble"><h1>Apache 2.0 Thread Safety Issues</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/developer/thread_safety.html" title="English">&nbsp;en&nbsp;</a></p>
</div>

    <p>When using any of the threaded mpms in Apache 2.0 it is important
    that every function called from Apache be thread safe.  When linking in 3rd
    party extensions it can be difficult to determine whether the resulting
    server will be thread safe.  Casual testing generally won't tell you this
    either as thread safety problems can lead to subtle race conditons that
    may only show up in certain conditions under heavy load.</p>
</div>
<div id="quickview"><ul id="toc"><li><img alt="" src="../images/down.gif" /> <a href="#variables">Global and static variables</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#errno">errno</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#functions">Common standard troublesome functions</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#commonlibs">Common 3rd Party Libraries</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#liblist">Library List</a></li>
</ul></div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="variables" id="variables">Global and static variables</a></h2>
    <p>When writing your module or when trying to determine if a module or
    3rd party library is thread safe there are some common things to keep in
    mind.</p>

    <p>First, you need to recognize that in a threaded model each individual
    thread has its own program counter, stack and registers.  Local variables
    live on the stack, so those are fine.  You need to watch out for any
    static or global variables.  This doesn't mean that you are absolutely not
    allowed to use static or global variables.  There are times when you
    actually want something to affect all threads, but generally you need to
    avoid using them if you want your code to be thread safe.</p>
   
    <p>In the case where you have a global variable that needs to be global and
    accessed by all threads, be very careful when you update it.  If, for
    example, it is an incrementing counter, you need to atomically increment
    it to avoid race conditions with other threads.  You do this using a mutex
    (mutual exclusion). Lock the mutex, read the current value, increment it
    and write it back and then unlock the mutex.  Any other thread that wants
    to modify the value has to first check the mutex and block until it is
    cleared.</p>

    <p>If you are using <a href="http://apr.apache.org/">APR</a>, have a look
    at the <code>apr_atomic_<var>*</var></code> functions and the
    <code>apr_thread_mutex_<var>*</var></code> functions.</p>
    
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="errno" id="errno">errno</a></h2>
    <p>This is a common global variable that holds the error number of the
    last error that occurred. If one thread calls a low-level function that
    sets errno and then another thread checks it, we are bleeding error
    numbers from one thread into another.  To solve this, make sure your
    module or library defines <code>_REENTRANT</code> or is compiled with
    <code>-D_REENTRANT</code>. This will make errno a per-thread variable
    and should hopefully be transparent to the code. It does this by doing
    something like this:</p>

    <div class="example"><p><code>
      #define errno (*(__errno_location()))
    </code></p></div>

    <p>which means that accessing errno will call
    <code>__errno_location()</code> which is provided by the libc. Setting
    <code>_REENTRANT</code> also forces redefinition of some other functions
    to their <code><var>*</var>_r</code> equivalents and sometimes changes
    the common <code>getc</code>/<code>putc</code> macros into safer function
    calls.  Check your libc documentation for specifics.  Instead of, or in
    addition to <code>_REENTRANT</code> the symbols that may affect this are 
    <code>_POSIX_C_SOURCE</code>, <code>_THREAD_SAFE</code>,
    <code>_SVID_SOURCE</code>, and <code>_BSD_SOURCE</code>.</p>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="functions" id="functions">Common standard troublesome functions</a></h2>
    <p>Not only do things have to be thread safe, but they also have to be
    reentrant. <code>strtok()</code> is an obvious one. You call it the first
    time with your delimiter which it then remembers and on each subsequent
    call it returns the next token.  Obviously if multiple threads are
    calling it you will have a problem.  Most systems have a reentrant version
    of of the function called <code>strtok_r()</code> where you pass in an
    extra argument which contains an allocated <code>char *</code> which the
    function will use instead of its own static storage for maintaining
    the tokenizing state. If you are using <a href="http://apr.apache.org/">APR</a> you can use <code>apr_strtok()</code>.</p>

    <p><code>crypt()</code> is another function that tends to not be reentrant,
    so if you run across calls to that function in a library, watch out. On
    some systems it is reentrant though, so it is not always a problem. If
    your system has <code>crypt_r()</code> chances are you should be using
    that, or if possible simply avoid the whole mess by using md5 instead.</p>
    
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="commonlibs" id="commonlibs">Common 3rd Party Libraries</a></h2>
    <p>The following is a list of common libraries that are used by 3rd party
    Apache modules.  You can check to see if your module is using a potentially
    unsafe library by using tools such as <code>ldd(1)</code> and
    <code>nm(1)</code>. For <a href="http://www.php.net/">PHP</a>, for example,
    try this:</p>

    <div class="example"><p><code>
      % ldd libphp4.so<br />
      libsablot.so.0 =&gt; /usr/local/lib/libsablot.so.0 (0x401f6000)<br />
      libexpat.so.0 =&gt; /usr/lib/libexpat.so.0 (0x402da000)<br />
      libsnmp.so.0 =&gt; /usr/lib/libsnmp.so.0 (0x402f9000)<br />
      libpdf.so.1 =&gt; /usr/local/lib/libpdf.so.1 (0x40353000)<br />
      libz.so.1 =&gt; /usr/lib/libz.so.1 (0x403e2000)<br />
      libpng.so.2 =&gt; /usr/lib/libpng.so.2 (0x403f0000)<br />
      libmysqlclient.so.11 =&gt; /usr/lib/libmysqlclient.so.11 (0x40411000)<br />
      libming.so =&gt; /usr/lib/libming.so (0x40449000)<br />
      libm.so.6 =&gt; /lib/libm.so.6 (0x40487000)<br />
      libfreetype.so.6 =&gt; /usr/lib/libfreetype.so.6 (0x404a8000)<br />
      libjpeg.so.62 =&gt; /usr/lib/libjpeg.so.62 (0x404e7000)<br />
      libcrypt.so.1 =&gt; /lib/libcrypt.so.1 (0x40505000)<br />
      libssl.so.2 =&gt; /lib/libssl.so.2 (0x40532000)<br />
      libcrypto.so.2 =&gt; /lib/libcrypto.so.2 (0x40560000)<br />
      libresolv.so.2 =&gt; /lib/libresolv.so.2 (0x40624000)<br />
      libdl.so.2 =&gt; /lib/libdl.so.2 (0x40634000)<br />
      libnsl.so.1 =&gt; /lib/libnsl.so.1 (0x40637000)<br />
      libc.so.6 =&gt; /lib/libc.so.6 (0x4064b000)<br />
      /lib/ld-linux.so.2 =&gt; /lib/ld-linux.so.2 (0x80000000)
    </code></p></div>

    <p>In addition to these libraries you will need to have a look at any
    libraries linked statically into the module. You can use <code>nm(1)</code>
    to look for individual symbols in the module.</p>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="liblist" id="liblist">Library List</a></h2>
    <p>Please drop a note to <a href="http://httpd.apache.org/lists.html#http-dev"><EMAIL></a>
    if you have additions or corrections to this list.</p>

    <table class="bordered"><tr class="header"><th>Library</th><th>Version</th><th>Thread Safe?</th><th>Notes</th></tr>
<tr><td><a href="http://aspell.sourceforge.net/">ASpell/PSpell</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr class="odd"><td><a href="http://www.sleepycat.com/">Berkeley DB</a></td>
        <td>3.x, 4.x</td>
        <td>Yes</td>
        <td>Be careful about sharing a connection across threads.</td></tr>
<tr><td><a href="http://sources.redhat.com/bzip2/index.html">bzip2</a></td>
        <td> </td>
        <td>Yes</td>
        <td>Both low-level and high-level APIs are thread-safe. However,
        high-level API requires thread-safe access to errno.</td></tr>
<tr class="odd"><td><a href="http://cr.yp.to/cdb.html">cdb</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://www.washington.edu/imap/">C-Client</a></td>
        <td> </td>
        <td>Perhaps</td>
        <td>c-client uses <code>strtok()</code> and
        <code>gethostbyname()</code> which are not thread-safe on most C
        library implementations.  c-client's static data is meant to be shared
        across threads. If <code>strtok()</code> and
        <code>gethostbyname()</code> are thread-safe on your OS, c-client
        <em>may</em> be thread-safe.</td></tr>
<tr class="odd"><td><a href="http://www.fastio.com/">cpdflib</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://www.ijg.org/files/">libcrypt</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr class="odd"><td><a href="http://expat.sourceforge.net/">Expat</a></td>
        <td> </td>
        <td>Yes</td>
        <td>Need a separate parser instance per thread</td></tr>
<tr><td><a href="http://www.freetds.org/">FreeTDS</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr class="odd"><td><a href="http://www.freetype.org/">FreeType</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://www.boutell.com/gd/">GD 1.8.x</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr class="odd"><td><a href="http://www.boutell.com/gd/">GD 2.0.x</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://www.gnu.org/software/gdbm/gdbm.html">gdbm</a></td>
        <td> </td>
        <td>No</td>
        <td>Errors returned via a static <code>gdbm_error</code>
        variable</td></tr>
<tr class="odd"><td><a href="http://www.imagemagick.org/">ImageMagick</a></td>
        <td>5.2.2</td>
        <td>Yes</td>
        <td>ImageMagick docs claim it is thread safe since version 5.2.2 (see <a href="http://www.cise.ufl.edu/depot/www/ImageMagick/www/Changelog.html">Change log</a>).
        </td></tr>
<tr><td><a href="http://www.enlightenment.org/pages/imlib2.html">Imlib2</a></td>
        <td> </td>
        <td>?</td>
        <td> </td></tr>
<tr class="odd"><td><a href="http://www.ijg.org/files/">libjpeg</a></td>
        <td>v6b</td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://mysql.com">libmysqlclient</a></td>
        <td> </td>
        <td>Yes</td>
        <td>Use mysqlclient_r library variant to ensure thread-safety.  For
            more information, please read <a href="http://www.mysql.com/doc/en/Threaded_clients.html">http://www.mysql.com/doc/en/Threaded_clients.html</a>.</td></tr>
<tr class="odd"><td><a href="http://www.opaque.net/ming/">Ming</a></td>
        <td>0.2a</td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://net-snmp.sourceforge.net/">Net-SNMP</a></td>
        <td>5.0.x</td>
        <td>?</td>
        <td> </td></tr>
<tr class="odd"><td><a href="http://www.openldap.org/">OpenLDAP</a></td>
        <td>2.1.x</td>
        <td>Yes</td>
        <td>Use <code>ldap_r</code> library variant to ensure
        thread-safety.</td></tr>
<tr><td><a href="http://www.openssl.org/">OpenSSL</a></td>
        <td>0.9.6g</td>
        <td>Yes</td>
        <td>Requires proper usage of <code>CRYPTO_num_locks</code>,
        <code>CRYPTO_set_locking_callback</code>,
        <code>CRYPTO_set_id_callback</code></td></tr>
<tr class="odd"><td><a href="http://www.oracle.com/">liboci8 (Oracle 8+)</a></td>
        <td>8.x,9.x</td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://pdflib.com/">pdflib</a></td>
        <td>5.0.x</td>
        <td>Yes</td>
        <td>PDFLib docs claim it is thread safe; changes.txt indicates it
            has been partially thread-safe since V1.91: <a href="http://www.pdflib.com/products/pdflib/index.html">http://www.pdflib.com/products/pdflib/index.html</a>.</td></tr>
<tr class="odd"><td><a href="http://www.libpng.org/pub/png/libpng.html">libpng</a></td>
        <td>1.0.x</td>
        <td>?</td>
        <td> </td></tr>
<tr><td><a href="http://www.libpng.org/pub/png/libpng.html">libpng</a></td>
        <td>1.2.x</td>
        <td>?</td>
        <td> </td></tr>
<tr class="odd"><td><a href="http://www.postgresql.org/idocs/index.php?libpq-threading.html">libpq (PostgreSQL)</a></td>
        <td>7.x</td>
        <td>Yes</td>
        <td>Don't share connections across threads and watch out for
        <code>crypt()</code> calls</td></tr>
<tr><td><a href="http://www.gingerall.com/charlie/ga/xml/p_sab.xml">Sablotron</a></td>
        <td>0.95</td>
        <td>?</td>
        <td /></tr>
<tr class="odd"><td><a href="http://www.gzip.org/zlib/">zlib</a></td>
        <td>1.1.4</td>
        <td>Yes</td>
        <td>Relies upon thread-safe zalloc and zfree functions  Default is to
        use libc's calloc/free which are thread-safe.</td></tr>
</table>
</div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/developer/thread_safety.html" title="English">&nbsp;en&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>