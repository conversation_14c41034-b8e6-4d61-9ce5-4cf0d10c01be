<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>Authentication, Authorization and Access Control - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="./"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a> &gt; <a href="./">How-To / Tutorials</a></div><div id="page-content"><div id="preamble"><h1>Authentication, Authorization and Access Control</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/howto/auth.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/howto/auth.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/howto/auth.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div>

    <p>Authentication is any process by which you verify that
    someone is who they claim they are. Authorization is any
    process by which someone is allowed to be where they want to
    go, or to have information that they want to have.</p>
</div>
<div id="quickview"><ul id="toc"><li><img alt="" src="../images/down.gif" /> <a href="#related">Related Modules and Directives</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#introduction">Introduction</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#theprerequisites">The Prerequisites</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#gettingitworking">Getting it working</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#lettingmorethanonepersonin">Letting more than one
person in</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#possibleproblems">Possible problems</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#dbmdbd">Alternate password storage</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#moreinformation">More information</a></li>
</ul></div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="related" id="related">Related Modules and Directives</a></h2>

<p>There are three types of modules involved in the authentication and
authorization process.  You will usually need to choose at least one
module from each group.</p>

<ul>
  <li>Authentication type (see the 
      <code class="directive"><a href="../mod/core.html#authtype">AuthType</a></code> directive)
    <ul>
      <li><code class="module"><a href="../mod/mod_auth_basic.html">mod_auth_basic</a></code></li>
      <li><code class="module"><a href="../mod/mod_auth_digest.html">mod_auth_digest</a></code></li>
    </ul>
  </li>
  <li>Authentication provider
    <ul>
      <li><code class="module"><a href="../mod/mod_authn_alias.html">mod_authn_alias</a></code></li>
      <li><code class="module"><a href="../mod/mod_authn_anon.html">mod_authn_anon</a></code></li>
      <li><code class="module"><a href="../mod/mod_authn_dbd.html">mod_authn_dbd</a></code></li>
      <li><code class="module"><a href="../mod/mod_authn_dbm.html">mod_authn_dbm</a></code></li>
      <li><code class="module"><a href="../mod/mod_authn_default.html">mod_authn_default</a></code></li>
      <li><code class="module"><a href="../mod/mod_authn_file.html">mod_authn_file</a></code></li>
      <li><code class="module"><a href="../mod/mod_authnz_ldap.html">mod_authnz_ldap</a></code></li>
    </ul>
  </li>
  <li>Authorization (see the 
      <code class="directive"><a href="../mod/core.html#require">Require</a></code> directive)
    <ul>
      <li><code class="module"><a href="../mod/mod_authnz_ldap.html">mod_authnz_ldap</a></code></li>
      <li><code class="module"><a href="../mod/mod_authz_dbm.html">mod_authz_dbm</a></code></li>
      <li><code class="module"><a href="../mod/mod_authz_default.html">mod_authz_default</a></code></li>
      <li><code class="module"><a href="../mod/mod_authz_groupfile.html">mod_authz_groupfile</a></code></li>
      <li><code class="module"><a href="../mod/mod_authz_owner.html">mod_authz_owner</a></code></li>
      <li><code class="module"><a href="../mod/mod_authz_user.html">mod_authz_user</a></code></li>
    </ul>
  </li>
</ul>

  <p>The module <code class="module"><a href="../mod/mod_authnz_ldap.html">mod_authnz_ldap</a></code> is both an
  authentication and authorization provider.  The module
  <code class="module"><a href="../mod/mod_authn_alias.html">mod_authn_alias</a></code> is not an authentication provider
  in itself, but allows other authentication providers to be
  configured in a flexible manner.</p>

  <p>The module <code class="module"><a href="../mod/mod_authz_host.html">mod_authz_host</a></code> provides authorization
  and access control based on hostname, IP address or characteristics
  of the request, but is not part of the authentication provider
  system.</p>

  <p>You probably also want to take a look at the <a href="access.html">Access Control</a> howto, which discusses the
  various ways to control access to your server.</p>

</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="introduction" id="introduction">Introduction</a></h2>
    <p>If you have information on your web site that is sensitive
    or intended for only a small group of people, the techniques in
    this article will help you make sure that the people that see
    those pages are the people that you wanted to see them.</p>

    <p>This article covers the "standard" way of protecting parts
    of your web site that most of you are going to use.</p>

    <div class="note"><h3>Note:</h3>
    <p>If your data really needs to be secure, consider using
    <code class="module"><a href="../mod/mod_ssl.html">mod_ssl</a></code> in addition to any authentication.</p>
    </div>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="theprerequisites" id="theprerequisites">The Prerequisites</a></h2>
    <p>The directives discussed in this article will need to go
    either in your main server configuration file (typically in a
    <code class="directive"><a href="../mod/core.html#directory">&lt;Directory&gt;</a></code> section), or
    in per-directory configuration files (<code>.htaccess</code> files).</p>

    <p>If you plan to use <code>.htaccess</code> files, you will
    need to have a server configuration that permits putting
    authentication directives in these files. This is done with the
    <code class="directive"><a href="../mod/core.html#allowoverride">AllowOverride</a></code> directive, which
    specifies which directives, if any, may be put in per-directory
    configuration files.</p>

    <p>Since we're talking here about authentication, you will need
    an <code class="directive"><a href="../mod/core.html#allowoverride">AllowOverride</a></code> directive like the
    following:</p>

    <div class="example"><p><code>
      AllowOverride AuthConfig
    </code></p></div>

    <p>Or, if you are just going to put the directives directly in
    your main server configuration file, you will of course need to
    have write permission to that file.</p>

    <p>And you'll need to know a little bit about the directory
    structure of your server, in order to know where some files are
    kept. This should not be terribly difficult, and I'll try to
    make this clear when we come to that point.</p>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="gettingitworking" id="gettingitworking">Getting it working</a></h2>
    <p>Here's the basics of password protecting a directory on your
    server.</p>

    <p>First, you need to create a password file. Exactly how you do
    this will vary depending on what authentication provider you have
    chosen. More on that later. To start with, we'll use a text password
    file.</p>

    <p>This file should be
    placed somewhere not accessible from the web. This is so that
    folks cannot download the password file. For example, if your
    documents are served out of <code>/usr/local/apache/htdocs</code> you
    might want to put the password file(s) in
    <code>/usr/local/apache/passwd</code>.</p>

    <p>To create the file, use the <code class="program"><a href="../programs/htpasswd.html">htpasswd</a></code> utility that
    came with Apache. This will be located in the <code>bin</code> directory
    of wherever you installed Apache. If you have installed Apache from
    a third-party package, it may be in your execution path.</p>
    
    <p>To create the file, type:</p>

    <div class="example"><p><code>
      htpasswd -c /usr/local/apache/passwd/passwords rbowen
    </code></p></div>

    <p><code class="program"><a href="../programs/htpasswd.html">htpasswd</a></code> will ask you for the password, and
    then ask you to type it again to confirm it:</p>

    <div class="example"><p><code>
      # htpasswd -c /usr/local/apache/passwd/passwords rbowen<br />
      New password: mypassword<br />
      Re-type new password: mypassword<br />
      Adding password for user rbowen
    </code></p></div>

    <p>If <code class="program"><a href="../programs/htpasswd.html">htpasswd</a></code> is not in your path, of course
    you'll have to type the full path to the file to get it to run.
    With a default installation, it's located at
    <code>/usr/local/apache2/bin/htpasswd</code></p>

    <p>Next, you'll need to configure the server to request a
    password and tell the server which users are allowed access.
    You can do this either by editing the <code>httpd.conf</code>
    file or using an <code>.htaccess</code> file. For example, if
    you wish to protect the directory
    <code>/usr/local/apache/htdocs/secret</code>, you can use the
    following directives, either placed in the file
    <code>/usr/local/apache/htdocs/secret/.htaccess</code>, or
    placed in <code>httpd.conf</code> inside a &lt;Directory
    /usr/local/apache/apache/htdocs/secret&gt; section.</p>

    <div class="example"><p><code>
      AuthType Basic<br />
      AuthName "Restricted Files"<br />
      # (Following line optional)<br />
      AuthBasicProvider file<br />
      AuthUserFile /usr/local/apache/passwd/passwords<br />
      Require user rbowen
    </code></p></div>

    <p>Let's examine each of those directives individually. The <code class="directive"><a href="../mod/core.html#authtype">AuthType</a></code> directive selects
    that method that is used to authenticate the user. The most
    common method is <code>Basic</code>, and this is the method
    implemented by <code class="module"><a href="../mod/mod_auth_basic.html">mod_auth_basic</a></code>. It is important to be aware,
    however, that Basic authentication sends the password from the client to
    the server unencrypted. This method should therefore not be used for
    highly sensitive data, unless accompanied by <code class="module"><a href="../mod/mod_ssl.html">mod_ssl</a></code>.
    Apache supports one other authentication method:
    <code>AuthType Digest</code>. This method is implemented by <code class="module"><a href="../mod/mod_auth_digest.html">mod_auth_digest</a></code> and is much more secure. Most recent
    browsers support Digest authentication.</p>

    <p>The <code class="directive"><a href="../mod/core.html#authname">AuthName</a></code> directive sets
    the <dfn>Realm</dfn> to be used in the authentication. The realm serves
    two major functions. First, the client often presents this information to
    the user as part of the password dialog box. Second, it is used by the
    client to determine what password to send for a given authenticated
    area.</p>

    <p>So, for example, once a client has authenticated in the
    <code>"Restricted Files"</code> area, it will automatically
    retry the same password for any area on the same server that is
    marked with the <code>"Restricted Files"</code> Realm.
    Therefore, you can prevent a user from being prompted more than
    once for a password by letting multiple restricted areas share
    the same realm. Of course, for security reasons, the client
    will always need to ask again for the password whenever the
    hostname of the server changes.</p>

    <p>The <code class="directive"><a href="../mod/mod_auth_basic.html#authbasicprovider">AuthBasicProvider</a></code> is,
    in this case, optional, since <code>file</code> is the default value
    for this directive. You'll need to use this directive if you are
    choosing a different source for authentication, such as
    <code class="module"><a href="../mod/mod_authn_dbm.html">mod_authn_dbm</a></code> or <code class="module"><a href="../mod/mod_authn_dbd.html">mod_authn_dbd</a></code>.</p>

    <p>The <code class="directive"><a href="../mod/mod_authn_file.html#authuserfile">AuthUserFile</a></code>
    directive sets the path to the password file that we just
    created with <code class="program"><a href="../programs/htpasswd.html">htpasswd</a></code>. If you have a large number
    of users, it can be quite slow to search through a plain text
    file to authenticate the user on each request. Apache also has
    the ability to store user information in fast database files.
    The <code class="module"><a href="../mod/mod_authn_dbm.html">mod_authn_dbm</a></code> module provides the <code class="directive"><a href="../mod/mod_authn_dbm.html#authdbmuserfile">AuthDBMUserFile</a></code> directive. These
    files can be created and manipulated with the <code class="program"><a href="../programs/dbmmanage.html">dbmmanage</a></code> program. Many
    other types of authentication options are available from third
    party modules in the <a href="http://modules.apache.org/">Apache Modules
    Database</a>.</p>

    <p>Finally, the <code class="directive"><a href="../mod/core.html#require">Require</a></code>
    directive provides the authorization part of the process by
    setting the user that is allowed to access this region of the
    server. In the next section, we discuss various ways to use the
    <code class="directive"><a href="../mod/core.html#require">Require</a></code> directive.</p>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="lettingmorethanonepersonin" id="lettingmorethanonepersonin">Letting more than one
person in</a></h2>
    <p>The directives above only let one person (specifically
    someone with a username of <code>rbowen</code>) into the
    directory. In most cases, you'll want to let more than one
    person in. This is where the <code class="directive"><a href="../mod/mod_authz_groupfile.html#authgroupfile">AuthGroupFile</a></code> comes in.</p>

    <p>If you want to let more than one person in, you'll need to
    create a group file that associates group names with a list of
    users in that group. The format of this file is pretty simple,
    and you can create it with your favorite editor. The contents
    of the file will look like this:</p>

   <div class="example"><p><code>
     GroupName: rbowen dpitts sungo rshersey
   </code></p></div>

    <p>That's just a list of the members of the group in a long
    line separated by spaces.</p>

    <p>To add a user to your already existing password file,
    type:</p>

    <div class="example"><p><code>
      htpasswd /usr/local/apache/passwd/passwords dpitts
    </code></p></div>

    <p>You'll get the same response as before, but it will be
    appended to the existing file, rather than creating a new file.
    (It's the <code>-c</code> that makes it create a new password
    file).</p>

    <p>Now, you need to modify your <code>.htaccess</code> file to
    look like the following:</p>

    <div class="example"><p><code>
      AuthType Basic<br />
      AuthName "By Invitation Only"<br />
      # Optional line:<br />
      AuthBasicProvider file<br />
      AuthUserFile /usr/local/apache/passwd/passwords<br />
      AuthGroupFile /usr/local/apache/passwd/groups<br />
      Require group GroupName
    </code></p></div>

    <p>Now, anyone that is listed in the group <code>GroupName</code>,
    and has an entry in the <code>password</code> file, will be let in, if
    they type the correct password.</p>

    <p>There's another way to let multiple users in that is less
    specific. Rather than creating a group file, you can just use
    the following directive:</p>

    <div class="example"><p><code>
      Require valid-user
    </code></p></div>

    <p>Using that rather than the <code>Require user rbowen</code>
    line will allow anyone in that is listed in the password file,
    and who correctly enters their password. You can even emulate
    the group behavior here, by just keeping a separate password
    file for each group. The advantage of this approach is that
    Apache only has to check one file, rather than two. The
    disadvantage is that you have to maintain a bunch of password
    files, and remember to reference the right one in the
    <code class="directive"><a href="../mod/mod_authn_file.html#authuserfile">AuthUserFile</a></code> directive.</p>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="possibleproblems" id="possibleproblems">Possible problems</a></h2>
    <p>Because of the way that Basic authentication is specified,
    your username and password must be verified every time you
    request a document from the server. This is even if you're
    reloading the same page, and for every image on the page (if
    they come from a protected directory). As you can imagine, this
    slows things down a little. The amount that it slows things
    down is proportional to the size of the password file, because
    it has to open up that file, and go down the list of users
    until it gets to your name. And it has to do this every time a
    page is loaded.</p>

    <p>A consequence of this is that there's a practical limit to
    how many users you can put in one password file. This limit
    will vary depending on the performance of your particular
    server machine, but you can expect to see slowdowns once you
    get above a few hundred entries, and may wish to consider a
    different authentication method at that time.</p>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="dbmdbd" id="dbmdbd">Alternate password storage</a></h2>

    <p>Because storing passwords in plain text files has the above
    problems, you may wish to store your passwords somewhere else, such
    as in a database.</p>

    <p><code class="module"><a href="../mod/mod_authn_dbm.html">mod_authn_dbm</a></code> and <code class="module"><a href="../mod/mod_authn_dbd.html">mod_authn_dbd</a></code>
    are two modules which make this possible. Rather than selecting
    <code class="directive"><a href="../mod/mod_auth_basic.html#authbasicsource">AuthBasicSource</a></code> file,
    instead you can choose <code>dbm</code> or <code>dbd</code> as your
    storage format.</p>

    <p>To select a dbd file rather than a text file, for example:</p>

    <div class="example"><p><code>
    &lt;Directory /www/docs/private&gt;<br />
    AuthName "Private"<br />
    AuthType Basic<br />
    AuthBasicProvider dbm<br />
    AuthDBMUserFile /www/passwords/passwd.dbm<br />
    Require valid-user
    </code></p></div>

    <p>Other options are available. Consult the
    <code class="module"><a href="../mod/mod_authn_dbm.html">mod_authn_dbm</a></code> documentation for more details.</p>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="moreinformation" id="moreinformation">More information</a></h2>
    <p>You should also read the documentation for
    <code class="module"><a href="../mod/mod_auth_basic.html">mod_auth_basic</a></code> and <code class="module"><a href="../mod/mod_authz_host.html">mod_authz_host</a></code> which
    contain some more information about how this all works.
    <code class="module"><a href="../mod/mod_authn_alias.html">mod_authn_alias</a></code> can also help in simplifying certain
    authentication configurations.</p>

    <p>And you may want to look at the <a href="access.html">Access
    Control</a> howto, which discusses a number of related topics.</p>

</div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/howto/auth.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/howto/auth.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/howto/auth.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>