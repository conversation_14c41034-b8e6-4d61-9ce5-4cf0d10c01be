#! /bin/sh
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
#
# The build environment was provided by <PERSON><PERSON><PERSON>.

# cwd must be top_srcdir
test -f build/sysv_makefile || exit 2

test -f bsd_converted || exit 1

tmpfile=`mktemp /tmp/sysv_makefile.XXXXXX 2>/dev/null` || tmpfile="tmp.$$"
for i in build/*.mk; do
    sed 's/^\.include "\(.*\)"/include \1/' $i >$tmpfile \
        && cp $tmpfile $i
done
rm -f $tmpfile

rm bsd_converted
exit 0
