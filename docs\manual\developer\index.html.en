<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>Developer Documentation for Apache 2.0 - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="../"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a></div><div id="page-content"><div id="preamble"><h1>Developer Documentation for Apache 2.0</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/developer/" title="English">&nbsp;en&nbsp;</a></p>
</div>

    <p>Many of the documents on these Developer pages are lifted
    from Apache 1.3's documentation. While they are all being
    updated to Apache 2.0, they are in different stages of
    progress. Please be patient, and point out any discrepancies or
    errors on the developer/ pages directly to the
    <a href="http://httpd.apache.org/lists.html#http-dev"><EMAIL></a> mailing list.</p>
</div>
<div id="quickview"><ul id="toc"><li><img alt="" src="../images/down.gif" /> <a href="#topics">Topics</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#external">External Resources</a></li>
</ul></div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="topics" id="topics">Topics</a></h2>
    <ul>
      <li><a href="API.html">Apache 1.3 API Notes</a></li>
      <li><a href="hooks.html">Apache 2.0 Hook Functions</a></li>
      <li><a href="request.html">Request Processing in Apache 2.0</a></li>
      <li><a href="filters.html">How filters work in Apache 2.0</a></li>
      <li><a href="modules.html">Converting Modules from Apache 1.3 to Apache 2.0</a></li>
      <li><a href="debugging.html">Debugging Memory Allocation in APR</a></li>
      <li><a href="documenting.html">Documenting Apache 2.0</a></li>
      <li><a href="thread_safety.html">Apache 2.0 Thread Safety Issues</a></li>
    </ul>
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="external" id="external">External Resources</a></h2>
    <ul>
      <li>Tools provided by Ian Holsman:
      <ul>
        <li><a href="http://lxr.webperf.org/">Apache 2 cross reference</a></li>
        <li><a href="http://docx.webperf.org/">Autogenerated Apache 2 code documentation</a></li>
      </ul></li>

      <li>Module Development Tutorials by Kevin O'Donnell
      <ul>
        <li><a href="http://threebit.net/tutorials/apache2_modules/tut1/tutorial1.html">Integrating a module into the Apache build system</a></li>

        <li><a href="http://threebit.net/tutorials/apache2_modules/tut2/tutorial2.html">Handling configuration directives</a></li>
      </ul></li>

      <li><a href="http://www.onlamp.com/pub/ct/38">Some notes on 
      Apache module development by Ryan Bloom</a></li>

      <li>Developer articles at <a href="http://www.apachetutor.org/">apachetutor</a> include:
      <ul>
        <li><a href="http://www.apachetutor.org/dev/request">Request Processing in Apache</a></li>
        <li><a href="http://www.apachetutor.org/dev/config">Configuration for Modules</a></li>
        <li><a href="http://www.apachetutor.org/dev/pools">Resource Management in Apache</a></li>
        <li><a href="http://www.apachetutor.org/dev/reslist">Connection Pooling in Apache</a></li>
        <li><a href="http://www.apachetutor.org/dev/brigades">Introduction to Buckets and Brigades</a></li>
      </ul></li>
    </ul>
</div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/developer/" title="English">&nbsp;en&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>