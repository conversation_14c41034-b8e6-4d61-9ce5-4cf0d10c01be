<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>Error Messages - Frequently Asked Questions - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page" class="no-sidebar"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="./"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a> &gt; <a href="./">FAQ</a></div><div id="page-content"><div id="preamble"><h1>Error Messages - Frequently Asked Questions</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/faq/error.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/faq/error.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/faq/error.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="error" id="error">Error Messages</a></h2>
    <ul>
    <li><a href="#error.sendfile">Invalid argument:
    core_output_filter: writing data to the network</a></li>
    <li><a href="#error.acceptex">AcceptEx failed</a></li>
    <li><a href="#error.scriptheaders">Premature end of script 
    headers</a></li>
    <li><a href="#error.permissiondenied">Permission denied</a></li>
    </ul>

    <h3><a name="error.sendfile" id="error.sendfile">Invalid argument:
    core_output_filter: writing data to the network</a></h3>

    <p>Apache uses the <code>sendfile</code> syscall on platforms
    where it is available in order to speed sending of responses.
    Unfortunately, on some systems, Apache will detect the presence of
    <code>sendfile</code> at compile-time, even when it does not work
    properly.  This happens most frequently when using network or
    other non-standard file-system.</p>

    <p>Symptoms of this problem include the above message in the error
    log and zero-length responses to non-zero-sized files.  The
    problem generally occurs only for static files, since dynamic
    content usually does not make use of <code>sendfile</code>.</p>

    <p>To fix this problem, simply use the <code class="directive"><a href="../mod/core.html#enablesendfile">EnableSendfile</a></code> directive to disable
    <code>sendfile</code> for all or part of your server.  Also see
    the <code class="directive"><a href="../mod/core.html#enablemmap">EnableMMAP</a></code>, which can
    help with similar problems.</p>

    

    <h3><a name="error.acceptex" id="error.acceptex">AcceptEx Failed</a></h3>

    <p>If you get error messages related to the <code>AcceptEx</code> syscall
    on win32, see the <code class="directive"><a href="../mod/mpm_winnt.html#win32disableacceptex">Win32DisableAcceptEx</a></code>
    directive.</p>
    


    <h3><a name="error.scriptheaders" id="error.scriptheaders">Premature end of script 
    headers</a></h3>

    <p>Most problems with CGI scripts result in this message written in the
    error log together with an <code>Internal Server Error</code> delivered
    to the browser.  A guide to helping debug this type of problem is
    available in the <a href="../howto/cgi.html#troubleshoot">CGI
    tutorial</a>.</p>
    

    <h3><a name="error.permissiondenied" id="error.permissiondenied">Permission denied</a></h3>

    <p>A <code>Permission denied</code> error in the
    <code>error_log</code>, accompanied by a <code>Forbidden</code>
    message to the client usually indicates a problem with your
    filesystem permissions, rather than a problem in the Apache HTTP
    Server configuration files.  Check to make sure that the
    <code class="directive"><a href="../mod/mpm_common.html#user">User</a></code> and <code class="directive"><a href="../mod/mpm_common.html#group">Group</a></code> running the child processes
    has adequate permission to access the files in question.  Also
    check that the directory and all parent directories are at least
    searchable for that user and group (i.e., <code>chmod
    +x</code>).</p>

    <p>Recent releases of Fedora Core and other Linux distributions
    using SELinux have additional access restrictions beyond those
    used by the basic filesystem.  Violations of these restrictions
    will also result in a <code>Permission denied</code> message.  See
    the <a href="http://fedora.redhat.com/docs/selinux-faq-fc3/">Fedora
    SELinux FAQ</a> and <a href="http://fedora.redhat.com/docs/selinux-apache-fc3/">Apache
    SELinux Policy Document</a>.</p>

   

</div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/faq/error.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/faq/error.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/faq/error.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>