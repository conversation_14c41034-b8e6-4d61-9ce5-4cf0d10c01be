.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.\" DO NOT EDIT! Generated from XML source.
.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.de Sh \" Subsection
.br
.if t .Sp
.ne 5
.PP
\fB\\$1\fR
.PP
..
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Ip \" List item
.br
.ie \\n(.$>=3 .ne \\$3
.el .ne 3
.IP "\\$1" \\$2
..
.TH "SUEXEC" 8 "2005-11-13" "Apache HTTP Server" "suexec"

.SH NAME
suexec \- Switch user before executing external programs

.SH "SYNOPSIS"
 
.PP
\fBsuexec\fR -\fBV\fR
 

.SH "SUMMARY"
 
.PP
suexec is used by the Apache HTTP Server to switch to another user before executing CGI programs\&. In order to achieve this, it must run as root\&. Since the HTTP daemon normally doesn't run as root, the suexec executable needs the setuid bit set and must be owned by root\&. It should never be writable for any other person than root\&.
 
.PP
For further information about the concepts and and the security model of suexec please refer to the suexec documentation (http://httpd\&.apache\&.org/docs/2\&.2/suexec\&.html)\&.
 

.SH "OPTIONS"
 
 
.TP
-V
If you are root, this option displays the compile options of suexec\&. For security reasons all configuration options are changeable only at compile time\&.  
 
