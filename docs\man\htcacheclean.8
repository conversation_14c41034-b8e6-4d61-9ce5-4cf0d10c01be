.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.\" DO NOT EDIT! Generated from XML source.
.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.de Sh \" Subsection
.br
.if t .Sp
.ne 5
.PP
\fB\\$1\fR
.PP
..
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Ip \" List item
.br
.ie \\n(.$>=3 .ne \\$3
.el .ne 3
.IP "\\$1" \\$2
..
.TH "HTCACHECLEAN" 8 "2005-08-08" "Apache HTTP Server" "htcacheclean"

.SH NAME
htcacheclean \- Clean up the disk cache

.SH "SYNOPSIS"
 
.PP
\fBhtcacheclean\fR [ -\fBD\fR ] [ -\fBv\fR ] [ -\fBt\fR ] [ -\fBr\fR ] [ -\fBn\fR ] -\fBp\fR\fIpath\fR -\fBl\fR\fIlimit\fR
 
.PP
\fBhtcacheclean\fR -\fBb\fR [ -\fBn\fR ] [ -\fBt\fR ] [ -\fBi\fR ] -\fBd\fR\fIinterval\fR -\fBp\fR\fIpath\fR -\fBl\fR\fIlimit\fR
 

.SH "SUMMARY"
 
.PP
htcacheclean is used to keep the size of mod_disk_cache's storage within a certain limit\&. This tool can run either manually or in daemon mode\&. When running in daemon mode, it sleeps in the background and checks the cache directories at regular intervals for cached content to be removed\&. You can stop the daemon cleanly by sending it a TERM or INT signal\&.
 

.SH "OPTIONS"
 
 
.TP
-d\fIinterval\fR
Daemonize and repeat cache cleaning every \fIinterval\fR minutes\&. This option is mutually exclusive with the -D, -v and -r options\&. To shutdown the daemon cleanly, just send it a SIGTERM or SIGINT\&.  
.TP
-D
Do a dry run and don't delete anything\&. This option is mutually exclusive with the -d option\&.  
.TP
-v
Be verbose and print statistics\&. This option is mutually exclusive with the -d option\&.  
.TP
-r
Clean thoroughly\&. This assumes that the Apache web server is not running (otherwise you may get garbage in the cache)\&. This option is mutually exclusive with the -d option and implies the -t option\&.  
.TP
-n
Be nice\&. This causes slower processing in favour of other processes\&. htcacheclean will sleep from time to time so that (a) the disk IO will be delayed and (b) the kernel can schedule other processes in the meantime\&.  
.TP
-t
Delete all empty directories\&. By default only cache files are removed, however with some configurations the large number of directories created may require attention\&. If your configuration requires a very large number of directories, to the point that inode or file allocation table exhaustion may become an issue, use of this option is advised\&.  
.TP
-p\fIpath\fR
Specify \fIpath\fR as the root directory of the disk cache\&. This should be the same value as specified with the CacheRoot directive\&.  
.TP
-l\fIlimit\fR
Specify \fIlimit\fR as the total disk cache size limit\&. The value is expressed in bytes by default (or attaching B to the number)\&. Attach K for Kbytes or M for MBytes\&.  
.TP
-i
Be intelligent and run only when there was a modification of the disk cache\&. This option is only possible together with the -d option\&.  
 
.SH "EXIT STATUS"
 
.PP
htcacheclean returns a zero status ("true") if all operations were successful, 1 otherwise\&.
 
