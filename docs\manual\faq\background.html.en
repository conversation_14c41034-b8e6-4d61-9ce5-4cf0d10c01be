<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>Background - Frequently Asked Questions - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page" class="no-sidebar"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="./"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a> &gt; <a href="./">FAQ</a></div><div id="page-content"><div id="preamble"><h1>Background - Frequently Asked Questions</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/faq/background.html" title="English">&nbsp;en&nbsp;</a></p>
</div>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="background" id="background">Background</a></h2>

    <ul>
    <li><a href="#background.what-is-apache">What is Apache?</a></li>
    <li><a href="#background.what-is-httpd">What is the Apache HTTP 
    Server?</a></li>
    <li><a href="#background.tested">How thoroughly tested is 
    Apache?</a></li>
    <li><a href="#background.logo">May I use the Apache logo on my 
    product or Web site?</a></li>
    </ul>
    
    <h3><a name="background.what-is-apache" id="background.what-is-apache">What is Apache?</a></h3>
      

      <p>The Apache Software Foundation (ASF) is a 501(c)3 non-profit
      organization providing support for the Apache community of
      open-sourced software projects.  For more details, please see the <a href="http://www.apache.org/foundation/faq.html">Apache Software
      Foundation FAQ</a></p>

      <p>The Apache HTTP Server -- sometimes called Apache httpd -- is
      a project of the Apache Software foundation aimed at creating a
      robust, commercial-grade, featureful, and freely-available
      source code implementation of an HTTP (Web) server.  For more
      information, please see the <a href="http://httpd.apache.org/ABOUT_APACHE.html">About
      Apache</a> page.</p>

    
	
	<h3><a name="background.what-is-httpd" id="background.what-is-httpd">What is the Apache HTTP Server?</a></h3>
      
      <ul>
        <li>is a powerful, flexible, HTTP/1.1 compliant web server</li>
        <li>implements the latest protocols, including HTTP/1.1 (RFC2616)</li>
        <li>is highly configurable and extensible with third-party modules</li>
        <li>can be customised by writing 'modules' using the Apache module
        API</li>
        <li>provides full source code and comes with an 
        <a href="http://www.apache.org/licenses/LICENSE-2.0">unrestrictive 
        license</a></li>
        <li>runs on Windows 2003/XP/2000/NT/9x, Netware 5.x and above, OS/2, 
        and most versions of Unix, as well as several other operating 
        systems</li>
        <li>is actively being developed</li>
        <li>encourages user feedback through new ideas, bug reports and
        patches</li>
      </ul>
	
	
	<h3><a name="background.tested" id="background.tested">How thoroughly tested is Apache?</a></h3>
      
      <p>Apache is run on millions of Internet servers.
      It has been tested thoroughly by both developers and users. The Apache
      HTTP Server Project maintains rigorous standards before releasing new 
      versions of our server, and our server runs without a hitch on over 
      70% of all WWW servers available on the Internet. When bugs do show 
      up, we release patches and new versions as soon as they are
      available.</p>
    

	<h3><a name="background.logo" id="background.logo">May I use the Apache logo on my product or Web site?</a></h3>
      
      <p>You may <em>NOT</em> use any original artwork from the Apache
      Software Foundation, nor make or use modified versions of such
      artwork, except under the following conditions:</p>
      <ul>
        <li>You may use the <a href="http://httpd.apache.org/apache_pb.gif">'Powered by Apache'
        graphic</a> on a Web site that is being served by the Apache HTTP
        server software.</li>
        <li>You may use the aforementioned <a href="http://httpd.apache.org/apache_pb.gif">'Powered by Apache'
        graphic</a> or the <a href="http://www.apache.org/images/asf_logo.gif">Apache Software
        Foundation logo</a> in product description and promotional material
        <em>IF and ONLY IF</em> such use can in no way be interpreted as
        anything other than an attribution. Using the Apache name and 
        artwork in a manner that implies endorsement of a product or
        service is strictly forbidden.</li>
      </ul>
    
 </div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/faq/background.html" title="English">&nbsp;en&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>