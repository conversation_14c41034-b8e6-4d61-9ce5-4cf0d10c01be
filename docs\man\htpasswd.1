.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.\" DO NOT EDIT! Generated from XML source.
.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.de Sh \" Subsection
.br
.if t .Sp
.ne 5
.PP
\fB\\$1\fR
.PP
..
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Ip \" List item
.br
.ie \\n(.$>=3 .ne \\$3
.el .ne 3
.IP "\\$1" \\$2
..
.TH "HTPASSWD" 1 "2003-11-25" "Apache HTTP Server" "htpasswd"

.SH NAME
htpasswd \- Manage user files for basic authentication

.SH "SYNOPSIS"
 
.PP
\fBhtpasswd\fR [ -\fBc\fR ] [ -\fBm\fR ] [ -\fBD\fR ] \fIpasswdfile\fR \fIusername\fR
 
.PP
\fBhtpasswd\fR -\fBb\fR [ -\fBc\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] [ -\fBD\fR ] \fIpasswdfile\fR \fIusername\fR \fIpassword\fR
 
.PP
\fBhtpasswd\fR -\fBn\fR [ -\fBm\fR | -\fBd\fR | -\fBs\fR | -\fBp\fR ] \fIusername\fR
 
.PP
\fBhtpasswd\fR -\fBnb\fR [ -\fBm\fR | -\fBd\fR | -\fBs\fR | -\fBp\fR ] \fIusername\fR \fIpassword\fR
 

.SH "SUMMARY"
 
.PP
htpasswd is used to create and update the flat-files used to store usernames and password for basic authentication of HTTP users\&. If htpasswd cannot access a file, such as not being able to write to the output file or not being able to read the file in order to update it, it returns an error status and makes no changes\&.
 
.PP
Resources available from the Apache HTTP server can be restricted to just the users listed in the files created by htpasswd\&. This program can only manage usernames and passwords stored in a flat-file\&. It can encrypt and display password information for use in other types of data stores, though\&. To use a DBM database see dbmmanage\&.
 
.PP
htpasswd encrypts passwords using either a version of MD5 modified for Apache, or the system's crypt() routine\&. Files managed by htpasswd may contain both types of passwords; some user records may have MD5-encrypted passwords while others in the same file may have passwords encrypted with crypt()\&.
 
.PP
This manual page only lists the command line arguments\&. For details of the directives necessary to configure user authentication in httpd see the Apache manual, which is part of the Apache distribution or can be found at http://httpd\&.apache\&.org/\&.
 

.SH "OPTIONS"
 
 
.TP
-b
Use batch mode; \fIi\&.e\&.\fR, get the password from the command line rather than prompting for it\&. This option should be used with extreme care, since \fBthe password is clearly visible\fR on the command line\&.  
.TP
-c
Create the \fIpasswdfile\fR\&. If \fIpasswdfile\fR already exists, it is rewritten and truncated\&. This option cannot be combined with the -n option\&.  
.TP
-n
Display the results on standard output rather than updating a file\&. This is useful for generating password records acceptable to Apache for inclusion in non-text data stores\&. This option changes the syntax of the command line, since the \fIpasswdfile\fR argument (usually the first one) is omitted\&. It cannot be combined with the -c option\&.  
.TP
-m
Use MD5 encryption for passwords\&. On Windows, Netware and TPF, this is the default\&.  
.TP
-d
Use crypt() encryption for passwords\&. The default on all platforms but Windows, Netware and TPF\&. Though possibly supported by htpasswd on all platforms, it is not supported by the httpd server on Windows, Netware and TPF\&.  
.TP
-s
Use SHA encryption for passwords\&. Facilitates migration from/to Netscape servers using the LDAP Directory Interchange Format (ldif)\&.  
.TP
-p
Use plaintext passwords\&. Though htpasswd will support creation on all platforms, the httpd daemon will only accept plain text passwords on Windows, Netware and TPF\&.  
.TP
-D
Delete user\&. If the username exists in the specified htpasswd file, it will be deleted\&.  
.TP
\fIpasswdfile\fR
Name of the file to contain the user name and password\&. If -c is given, this file is created if it does not already exist, or rewritten and truncated if it does exist\&.  
.TP
\fIusername\fR
The username to create or update in \fIpasswdfile\fR\&. If \fIusername\fR does not exist in this file, an entry is added\&. If it does exist, the password is changed\&.  
.TP
\fIpassword\fR
The plaintext password to be encrypted and stored in the file\&. Only used with the -b flag\&.  
 
.SH "EXIT STATUS"
 
.PP
htpasswd returns a zero status ("true") if the username and password have been successfully added or updated in the \fIpasswdfile\fR\&. htpasswd returns 1 if it encounters some problem accessing files, 2 if there was a syntax problem with the command line, 3 if the password was entered interactively and the verification entry didn't match, 4 if its operation was interrupted, 5 if a value is too long (username, filename, password, or final computed record), 6 if the username contains illegal characters (see the Restrictions section), and 7 if the file is not a valid password file\&.
 
.SH "EXAMPLES"
 
.nf

      htpasswd /usr/local/etc/apache/\&.htpasswd-users jsmith
    
.fi
 
.PP
Adds or modifies the password for user jsmith\&. The user is prompted for the password\&. If executed on a Windows system, the password will be encrypted using the modified Apache MD5 algorithm; otherwise, the system's crypt() routine will be used\&. If the file does not exist, htpasswd will do nothing except return an error\&.
 
.nf

      htpasswd -c /home/<USER>/public_html/\&.htpasswd jane
    
.fi
 
.PP
Creates a new file and stores a record in it for user jane\&. The user is prompted for the password\&. If the file exists and cannot be read, or cannot be written, it is not altered and htpasswd will display a message and return an error status\&.
 
.nf

      htpasswd -mb /usr/web/\&.htpasswd-all jones Pwd4Steve
    
.fi
 
.PP
Encrypts the password from the command line (Pwd4Steve) using the MD5 algorithm, and stores it in the specified file\&.
 
.SH "SECURITY CONSIDERATIONS"
 
.PP
Web password files such as those managed by htpasswd should \fInot\fR be within the Web server's URI space -- that is, they should not be fetchable with a browser\&.
 
.PP
The use of the -b option is discouraged, since when it is used the unencrypted password appears on the command line\&.
 
.SH "RESTRICTIONS"
 
.PP
On the Windows and MPE platforms, passwords encrypted with htpasswd are limited to no more than 255 characters in length\&. Longer passwords will be truncated to 255 characters\&.
 
.PP
The MD5 algorithm used by htpasswd is specific to the Apache software; passwords encrypted using it will not be usable with other Web servers\&.
 
.PP
Usernames are limited to 255 bytes and may not include the character :\&.
 
