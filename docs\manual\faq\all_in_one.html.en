<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>Frequently Asked Questions - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="./"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a> &gt; <a href="./">FAQ</a></div><div id="page-content"><div id="preamble"><h1>Frequently Asked Questions</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/faq/all_in_one.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/faq/all_in_one.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/faq/all_in_one.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div>

    <p>The latest version of this FAQ is always available from the main Apache
    web site, at &lt;<a href="http://httpd.apache.org/docs/2.2/faq/">http://httpd.apache.org/docs/2.2/faq/</a>&gt;.</p>

    <p>If you don't find the answer to your question in the below
    sections, please also consult the <a href="http://httpd.apache.org/docs/misc/FAQ.html">Apache 1.3
    FAQ</a> to see if your question is answered there.</p> 
</div>
<div id="quickview"><ul id="toc"><li><img alt="" src="../images/down.gif" /> <a href="#topics">Topics</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#background">Background</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#support">Support</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#error">Error Messages</a></li>
</ul></div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a id="topics" name="topics">Topics</a></h2>
<dl><dt><a href="#background">Background</a></dt><dd>Background information about the Apache HTTP Server.</dd>
<dt><a href="#support">Support</a></dt><dd>What do I do when I have problems?</dd>
<dt><a href="#error">Error Messages</a></dt><dd>What does this error message mean?</dd>
</dl></div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="background" id="background">Background</a></h2>

    <ul>
    <li><a href="#background.what-is-apache">What is Apache?</a></li>
    <li><a href="#background.what-is-httpd">What is the Apache HTTP 
    Server?</a></li>
    <li><a href="#background.tested">How thoroughly tested is 
    Apache?</a></li>
    <li><a href="#background.logo">May I use the Apache logo on my 
    product or Web site?</a></li>
    </ul>
    
    <h3><a name="background.what-is-apache" id="background.what-is-apache">What is Apache?</a></h3>
      

      <p>The Apache Software Foundation (ASF) is a 501(c)3 non-profit
      organization providing support for the Apache community of
      open-sourced software projects.  For more details, please see the <a href="http://www.apache.org/foundation/faq.html">Apache Software
      Foundation FAQ</a></p>

      <p>The Apache HTTP Server -- sometimes called Apache httpd -- is
      a project of the Apache Software foundation aimed at creating a
      robust, commercial-grade, featureful, and freely-available
      source code implementation of an HTTP (Web) server.  For more
      information, please see the <a href="http://httpd.apache.org/ABOUT_APACHE.html">About
      Apache</a> page.</p>

    
	
	<h3><a name="background.what-is-httpd" id="background.what-is-httpd">What is the Apache HTTP Server?</a></h3>
      
      <ul>
        <li>is a powerful, flexible, HTTP/1.1 compliant web server</li>
        <li>implements the latest protocols, including HTTP/1.1 (RFC2616)</li>
        <li>is highly configurable and extensible with third-party modules</li>
        <li>can be customised by writing 'modules' using the Apache module
        API</li>
        <li>provides full source code and comes with an 
        <a href="http://www.apache.org/licenses/LICENSE-2.0">unrestrictive 
        license</a></li>
        <li>runs on Windows 2003/XP/2000/NT/9x, Netware 5.x and above, OS/2, 
        and most versions of Unix, as well as several other operating 
        systems</li>
        <li>is actively being developed</li>
        <li>encourages user feedback through new ideas, bug reports and
        patches</li>
      </ul>
	
	
	<h3><a name="background.tested" id="background.tested">How thoroughly tested is Apache?</a></h3>
      
      <p>Apache is run on millions of Internet servers.
      It has been tested thoroughly by both developers and users. The Apache
      HTTP Server Project maintains rigorous standards before releasing new 
      versions of our server, and our server runs without a hitch on over 
      70% of all WWW servers available on the Internet. When bugs do show 
      up, we release patches and new versions as soon as they are
      available.</p>
    

	<h3><a name="background.logo" id="background.logo">May I use the Apache logo on my product or Web site?</a></h3>
      
      <p>You may <em>NOT</em> use any original artwork from the Apache
      Software Foundation, nor make or use modified versions of such
      artwork, except under the following conditions:</p>
      <ul>
        <li>You may use the <a href="http://httpd.apache.org/apache_pb.gif">'Powered by Apache'
        graphic</a> on a Web site that is being served by the Apache HTTP
        server software.</li>
        <li>You may use the aforementioned <a href="http://httpd.apache.org/apache_pb.gif">'Powered by Apache'
        graphic</a> or the <a href="http://www.apache.org/images/asf_logo.gif">Apache Software
        Foundation logo</a> in product description and promotional material
        <em>IF and ONLY IF</em> such use can in no way be interpreted as
        anything other than an attribution. Using the Apache name and 
        artwork in a manner that implies endorsement of a product or
        service is strictly forbidden.</li>
      </ul>
    
 </div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="support" id="support">Support</a></h2>
    <ul>
    <li><a href="#support.what2do">"Why can't I ...? Why won't ... work?" What
    to do in case of problems</a></li>

    <li><a href="#support.support">Whom do I contact for support?</a></li>
    </ul>

    <h3><a name="support.what2do" id="support.what2do">"Why can't I ...? Why won't ... work?" What to do in case of
    problems</a></h3>
    

      <p>If you are having trouble with your Apache server software, you should
      take the following steps:</p>

      <dl>
      <dt>Check the ErrorLog!</dt>
      <dd>Apache tries to be helpful when it encounters a problem. In many
      cases, it will provide some details by writing one or more messages to the
      server error log. Sometimes this is enough for you to diagnose &amp; fix
      the problem yourself (such as file permissions or the like). The default
      location of the error log is
      <code>/usr/local/apache2/logs/error_log</code>, but see the <code class="directive"><a href="../mod/core.html#errorlog">ErrorLog</a></code> directive in your config files for the
      location on your server.</dd>

      <dt>Check the ErrorLog again!</dt>
      <dd>Nearly all problems can be solved by reading the error log.</dd>
      
      <dt>Check the <a href="http://httpd.apache.org/docs/2.2/faq/">FAQ</a>!</dt>
      <dd>The latest version of the Apache Frequently-Asked Questions list can
      always be found at the main Apache web site.</dd>

      <dt>Check the Apache bug database</dt>
      <dd>Most problems that get reported to The Apache Group are recorded in
      the <a href="http://httpd.apache.org/bug_report.html">bug database</a>.
      <strong>Please</strong> check the existing reports, open
      <strong>and</strong> closed, before adding one. If you find that your
      issue has already been reported, please <em>don't</em> add a "me, too"
      report. If the original report isn't closed yet, we suggest that you
      check it periodically. You might also consider contacting the original
      submitter, because there may be an email exchange going on about the
      issue that isn't getting recorded in the database.</dd>

      <dt><a id="support.what2do.user-support" name="support.what2do.user-support">Ask in a user support forum</a></dt>
      <dd><p>Apache has an active community of users who are willing to share
      their knowledge. Participating in this community is usually the best and
      fastest way to get answers to your questions and problems.</p>

      <p><a href="http://httpd.apache.org/userslist.html">Users
      mailing list</a></p>

      <p><a href="irc://irc.freenode.net/#apache">#apache</a> on 
      <a href="http://freenode.net">Freenode IRC</a> is also available for 
      user support issues.</p>
      </dd>
      
      <dt>Please use the bug database for bugs!</dt>
      <dd><p>If you've gone through those steps above that are appropriate and
      have obtained no relief, then please <em>do</em> let the httpd developers
      know about the problem by <a href="http://httpd.apache.org/bug_report.html">logging a bug
      report</a>.</p>

      <p>If your problem involves the server crashing and generating a core
      dump, please <a href="http://httpd.apache.org/dev/debugging.html">
      include a backtrace</a> (if possible).</p>
      </dd>
      </dl>
    

    <h3><a name="support.support" id="support.support">Whom do I contact for support?</a></h3>
      <p>With millions of users and fewer than sixty volunteer developers,
      we cannot provide personal support for Apache. For free support, we
      suggest participating in a <a href="#support.what2do.user-support">user forum</a>.</p>

      <p>Professional, commercial support for Apache is available from
      <a href="http://www.apache.org/info/support.cgi">a number of
      companies</a>.</p>
    
</div><div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="error" id="error">Error Messages</a></h2>
    <ul>
    <li><a href="#error.sendfile">Invalid argument:
    core_output_filter: writing data to the network</a></li>
    <li><a href="#error.acceptex">AcceptEx failed</a></li>
    <li><a href="#error.scriptheaders">Premature end of script 
    headers</a></li>
    <li><a href="#error.permissiondenied">Permission denied</a></li>
    </ul>

    <h3><a name="error.sendfile" id="error.sendfile">Invalid argument:
    core_output_filter: writing data to the network</a></h3>

    <p>Apache uses the <code>sendfile</code> syscall on platforms
    where it is available in order to speed sending of responses.
    Unfortunately, on some systems, Apache will detect the presence of
    <code>sendfile</code> at compile-time, even when it does not work
    properly.  This happens most frequently when using network or
    other non-standard file-system.</p>

    <p>Symptoms of this problem include the above message in the error
    log and zero-length responses to non-zero-sized files.  The
    problem generally occurs only for static files, since dynamic
    content usually does not make use of <code>sendfile</code>.</p>

    <p>To fix this problem, simply use the <code class="directive"><a href="../mod/core.html#enablesendfile">EnableSendfile</a></code> directive to disable
    <code>sendfile</code> for all or part of your server.  Also see
    the <code class="directive"><a href="../mod/core.html#enablemmap">EnableMMAP</a></code>, which can
    help with similar problems.</p>

    

    <h3><a name="error.acceptex" id="error.acceptex">AcceptEx Failed</a></h3>

    <p>If you get error messages related to the <code>AcceptEx</code> syscall
    on win32, see the <code class="directive"><a href="../mod/mpm_winnt.html#win32disableacceptex">Win32DisableAcceptEx</a></code>
    directive.</p>
    


    <h3><a name="error.scriptheaders" id="error.scriptheaders">Premature end of script 
    headers</a></h3>

    <p>Most problems with CGI scripts result in this message written in the
    error log together with an <code>Internal Server Error</code> delivered
    to the browser.  A guide to helping debug this type of problem is
    available in the <a href="../howto/cgi.html#troubleshoot">CGI
    tutorial</a>.</p>
    

    <h3><a name="error.permissiondenied" id="error.permissiondenied">Permission denied</a></h3>

    <p>A <code>Permission denied</code> error in the
    <code>error_log</code>, accompanied by a <code>Forbidden</code>
    message to the client usually indicates a problem with your
    filesystem permissions, rather than a problem in the Apache HTTP
    Server configuration files.  Check to make sure that the
    <code class="directive"><a href="../mod/mpm_common.html#user">User</a></code> and <code class="directive"><a href="../mod/mpm_common.html#group">Group</a></code> running the child processes
    has adequate permission to access the files in question.  Also
    check that the directory and all parent directories are at least
    searchable for that user and group (i.e., <code>chmod
    +x</code>).</p>

    <p>Recent releases of Fedora Core and other Linux distributions
    using SELinux have additional access restrictions beyond those
    used by the basic filesystem.  Violations of these restrictions
    will also result in a <code>Permission denied</code> message.  See
    the <a href="http://fedora.redhat.com/docs/selinux-faq-fc3/">Fedora
    SELinux FAQ</a> and <a href="http://fedora.redhat.com/docs/selinux-apache-fc3/">Apache
    SELinux Policy Document</a>.</p>

   

</div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/faq/all_in_one.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/faq/all_in_one.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/faq/all_in_one.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>