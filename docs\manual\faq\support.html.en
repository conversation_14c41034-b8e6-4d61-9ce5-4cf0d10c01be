<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>Support - Frequently Asked Questions - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page" class="no-sidebar"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="./"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a> &gt; <a href="./">FAQ</a></div><div id="page-content"><div id="preamble"><h1>Support - Frequently Asked Questions</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/faq/support.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/faq/support.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/faq/support.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="support" id="support">Support</a></h2>
    <ul>
    <li><a href="#support.what2do">"Why can't I ...? Why won't ... work?" What
    to do in case of problems</a></li>

    <li><a href="#support.support">Whom do I contact for support?</a></li>
    </ul>

    <h3><a name="support.what2do" id="support.what2do">"Why can't I ...? Why won't ... work?" What to do in case of
    problems</a></h3>
    

      <p>If you are having trouble with your Apache server software, you should
      take the following steps:</p>

      <dl>
      <dt>Check the ErrorLog!</dt>
      <dd>Apache tries to be helpful when it encounters a problem. In many
      cases, it will provide some details by writing one or more messages to the
      server error log. Sometimes this is enough for you to diagnose &amp; fix
      the problem yourself (such as file permissions or the like). The default
      location of the error log is
      <code>/usr/local/apache2/logs/error_log</code>, but see the <code class="directive"><a href="../mod/core.html#errorlog">ErrorLog</a></code> directive in your config files for the
      location on your server.</dd>

      <dt>Check the ErrorLog again!</dt>
      <dd>Nearly all problems can be solved by reading the error log.</dd>
      
      <dt>Check the <a href="http://httpd.apache.org/docs/2.2/faq/">FAQ</a>!</dt>
      <dd>The latest version of the Apache Frequently-Asked Questions list can
      always be found at the main Apache web site.</dd>

      <dt>Check the Apache bug database</dt>
      <dd>Most problems that get reported to The Apache Group are recorded in
      the <a href="http://httpd.apache.org/bug_report.html">bug database</a>.
      <strong>Please</strong> check the existing reports, open
      <strong>and</strong> closed, before adding one. If you find that your
      issue has already been reported, please <em>don't</em> add a "me, too"
      report. If the original report isn't closed yet, we suggest that you
      check it periodically. You might also consider contacting the original
      submitter, because there may be an email exchange going on about the
      issue that isn't getting recorded in the database.</dd>

      <dt><a id="support.what2do.user-support" name="support.what2do.user-support">Ask in a user support forum</a></dt>
      <dd><p>Apache has an active community of users who are willing to share
      their knowledge. Participating in this community is usually the best and
      fastest way to get answers to your questions and problems.</p>

      <p><a href="http://httpd.apache.org/userslist.html">Users
      mailing list</a></p>

      <p><a href="irc://irc.freenode.net/#apache">#apache</a> on 
      <a href="http://freenode.net">Freenode IRC</a> is also available for 
      user support issues.</p>
      </dd>
      
      <dt>Please use the bug database for bugs!</dt>
      <dd><p>If you've gone through those steps above that are appropriate and
      have obtained no relief, then please <em>do</em> let the httpd developers
      know about the problem by <a href="http://httpd.apache.org/bug_report.html">logging a bug
      report</a>.</p>

      <p>If your problem involves the server crashing and generating a core
      dump, please <a href="http://httpd.apache.org/dev/debugging.html">
      include a backtrace</a> (if possible).</p>
      </dd>
      </dl>
    

    <h3><a name="support.support" id="support.support">Whom do I contact for support?</a></h3>
      <p>With millions of users and fewer than sixty volunteer developers,
      we cannot provide personal support for Apache. For free support, we
      suggest participating in a <a href="#support.what2do.user-support">user forum</a>.</p>

      <p>Professional, commercial support for Apache is available from
      <a href="http://www.apache.org/info/support.cgi">a number of
      companies</a>.</p>
    
</div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/faq/support.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/faq/support.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/faq/support.html" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>