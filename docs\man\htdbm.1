.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.\" DO NOT EDIT! Generated from XML source.
.\" XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
.de Sh \" Subsection
.br
.if t .Sp
.ne 5
.PP
\fB\\$1\fR
.PP
..
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Ip \" List item
.br
.ie \\n(.$>=3 .ne \\$3
.el .ne 3
.IP "\\$1" \\$2
..
.TH "HTDBM" 1 "2005-03-26" "Apache HTTP Server" "htdbm"

.SH NAME
htdbm \- Manipulate DBM password databases

.SH "SYNOPSIS"
 
.PP
\fBhtdbm\fR [ -\fBT\fR\fIDBTYPE\fR ] [ -\fBc\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] [ -\fBt\fR ] [ -\fBv\fR ] [ -\fBx\fR ] \fIfilename\fR \fIusername\fR
 
.PP
\fBhtdbm\fR -\fBb\fR [ -\fBT\fR\fIDBTYPE\fR ] [ -\fBc\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] [ -\fBt\fR ] [ -\fBv\fR ] \fIfilename\fR \fIusername\fR \fIpassword\fR
 
.PP
\fBhtdbm\fR -\fBn\fR [ -\fBc\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] [ -\fBt\fR ] [ -\fBv\fR ] \fIusername\fR
 
.PP
\fBhtdbm\fR -\fBnb\fR [ -\fBc\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] [ -\fBt\fR ] [ -\fBv\fR ] \fIusername\fR \fIpassword\fR
 
.PP
\fBhtdbm\fR -\fBv\fR [ -\fBT\fR\fIDBTYPE\fR ] [ -\fBc\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] [ -\fBt\fR ] [ -\fBv\fR ] \fIfilename\fR \fIusername\fR
 
.PP
\fBhtdbm\fR -\fBvb\fR [ -\fBT\fR\fIDBTYPE\fR ] [ -\fBc\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] [ -\fBt\fR ] [ -\fBv\fR ] \fIfilename\fR \fIusername\fR \fIpassword\fR
 
.PP
\fBhtdbm\fR -\fBx\fR [ -\fBT\fR\fIDBTYPE\fR ] [ -\fBm\fR | -\fBd\fR | -\fBp\fR | -\fBs\fR ] \fIfilename\fR \fIusername\fR
 
.PP
\fBhtdbm\fR -\fBl\fR [ -\fBT\fR\fIDBTYPE\fR ]
 

.SH "SUMMARY"
 
.PP
htdbm is used to manipulate the DBM format files used to store usernames and password for basic authentication of HTTP users via mod_auth_dbm\&. See the dbmmanage documentation for more information about these DBM files\&.
 

.SH "OPTIONS"
 
 
.TP
-b
Use batch mode; \fIi\&.e\&.\fR, get the password from the command line rather than prompting for it\&. This option should be used with extreme care, since \fBthe password is clearly visible\fR on the command line\&.  
.TP
-c
Create the \fIpasswdfile\fR\&. If \fIpasswdfile\fR already exists, it is rewritten and truncated\&. This option cannot be combined with the -n option\&.  
.TP
-n
Display the results on standard output rather than updating a database\&. This option changes the syntax of the command line, since the \fIpasswdfile\fR argument (usually the first one) is omitted\&. It cannot be combined with the -c option\&.  
.TP
-m
Use MD5 encryption for passwords\&. On Windows, Netware and TPF, this is the default\&.  
.TP
-d
Use crypt() encryption for passwords\&. The default on all platforms but Windows, Netware and TPF\&. Though possibly supported by htdbm on all platforms, it is not supported by the httpd server on Windows, Netware and TPF\&.  
.TP
-s
Use SHA encryption for passwords\&. Facilitates migration from/to Netscape servers using the LDAP Directory Interchange Format (ldif)\&.  
.TP
-p
Use plaintext passwords\&. Though htdbm will support creation on all platforms, the httpd daemon will only accept plain text passwords on Windows, Netware and TPF\&.  
.TP
-l
Print each of the usernames and comments from the database on stdout\&.  
.TP
-t
Interpret the final parameter as a comment\&. When this option is specified, an additional string can be appended to the command line; this string will be stored in the "Comment" field of the database, associated with the specified username\&.  
.TP
-v
Verify the username and password\&. The program will print a message indicating whether the supplied password is valid\&. If the password is invalid, the program exits with error code 3\&.  
.TP
-x
Delete user\&. If the username exists in the specified DBM file, it will be deleted\&.  
.TP
\fIfilename\fR
The filename of the DBM format file\&. Usually without the extension \&.db, \&.pag, or \&.dir\&. If -c is given, the DBM file is created if it does not already exist, or updated if it does exist\&.  
.TP
\fIusername\fR
The username to create or update in \fIpasswdfile\fR\&. If \fIusername\fR does not exist in this file, an entry is added\&. If it does exist, the password is changed\&.  
.TP
\fIpassword\fR
The plaintext password to be encrypted and stored in the DBM file\&. Used only with the -b flag\&.  
.TP
-T\fIDBTYPE\fR
Type of DBM file (SDBM, GDBM, DB, or "default")\&.  
 
.SH "BUGS"
 
.PP
One should be aware that there are a number of different DBM file formats in existence, and with all likelihood, libraries for more than one format may exist on your system\&. The three primary examples are SDBM, NDBM, GNU GDBM, and Berkeley/Sleepycat DB 2/3/4\&. Unfortunately, all these libraries use different file formats, and you must make sure that the file format used by \fIfilename\fR is the same format that htdbm expects to see\&. htdbm currently has no way of determining what type of DBM file it is looking at\&. If used against the wrong format, will simply return nothing, or may create a different DBM file with a different name, or at worst, it may corrupt the DBM file if you were attempting to write to it\&.
 
.PP
One can usually use the file program supplied with most Unix systems to see what format a DBM file is in\&.
 
.SH "EXIT STATUS"
 
.PP
htdbm returns a zero status ("true") if the username and password have been successfully added or updated in the DBM File\&. htdbm returns 1 if it encounters some problem accessing files, 2 if there was a syntax problem with the command line, 3 if the password was entered interactively and the verification entry didn't match, 4 if its operation was interrupted, 5 if a value is too long (username, filename, password, or final computed record), 6 if the username contains illegal characters (see the Restrictions section), and 7 if the file is not a valid DBM password file\&.
 
.SH "EXAMPLES"
 
.nf

      htdbm /usr/local/etc/apache/\&.htdbm-users jsmith
    
.fi
 
.PP
Adds or modifies the password for user jsmith\&. The user is prompted for the password\&. If executed on a Windows system, the password will be encrypted using the modified Apache MD5 algorithm; otherwise, the system's crypt() routine will be used\&. If the file does not exist, htdbm will do nothing except return an error\&.
 
.nf

      htdbm -c /home/<USER>/public_html/\&.htdbm jane
    
.fi
 
.PP
Creates a new file and stores a record in it for user jane\&. The user is prompted for the password\&. If the file exists and cannot be read, or cannot be written, it is not altered and htdbm will display a message and return an error status\&.
 
.nf

      htdbm -mb /usr/web/\&.htdbm-all jones Pwd4Steve
    
.fi
 
.PP
Encrypts the password from the command line (Pwd4Steve) using the MD5 algorithm, and stores it in the specified file\&.
 
.SH "SECURITY CONSIDERATIONS"
 
.PP
Web password files such as those managed by htdbm should \fInot\fR be within the Web server's URI space -- that is, they should not be fetchable with a browser\&.
 
.PP
The use of the -b option is discouraged, since when it is used the unencrypted password appears on the command line\&.
 
.SH "RESTRICTIONS"
 
.PP
On the Windows and MPE platforms, passwords encrypted with htdbm are limited to no more than 255 characters in length\&. Longer passwords will be truncated to 255 characters\&.
 
.PP
The MD5 algorithm used by htdbm is specific to the Apache software; passwords encrypted using it will not be usable with other Web servers\&.
 
.PP
Usernames are limited to 255 bytes and may not include the character :\&.
 
