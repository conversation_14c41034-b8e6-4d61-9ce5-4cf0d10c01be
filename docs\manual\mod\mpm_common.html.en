<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>mpm_common - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body>
<div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="./"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a> &gt; <a href="./">Modules</a></div>
<div id="page-content">
<div id="preamble"><h1>Apache MPM Common Directives</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../de/mod/mpm_common.html" hreflang="de" rel="alternate" title="Deutsch">&nbsp;de&nbsp;</a> |
<a href="../en/mod/mpm_common.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/mod/mpm_common.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a></p>
</div>
<table class="module"><tr><th><a href="module-dict.html#Description">Description:</a></th><td>A collection of directives that are implemented by
more than one multi-processing module (MPM)</td></tr>
<tr><th><a href="module-dict.html#Status">Status:</a></th><td>MPM</td></tr></table>
</div>
<div id="quickview"><h3 class="directives">Directives</h3>
<ul id="toc">
<li><img alt="" src="../images/down.gif" /> <a href="#acceptmutex">AcceptMutex</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#coredumpdirectory">CoreDumpDirectory</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#enableexceptionhook">EnableExceptionHook</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#gracefulshutdowntimeout">GracefulShutdownTimeout</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#group">Group</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#listen">Listen</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#listenbacklog">ListenBackLog</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#lockfile">LockFile</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#maxclients">MaxClients</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#maxmemfree">MaxMemFree</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#maxrequestsperchild">MaxRequestsPerChild</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#maxsparethreads">MaxSpareThreads</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#minsparethreads">MinSpareThreads</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#pidfile">PidFile</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#receivebuffersize">ReceiveBufferSize</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#scoreboardfile">ScoreBoardFile</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#sendbuffersize">SendBufferSize</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#serverlimit">ServerLimit</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#startservers">StartServers</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#startthreads">StartThreads</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#threadlimit">ThreadLimit</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#threadsperchild">ThreadsPerChild</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#threadstacksize">ThreadStackSize</a></li>
<li><img alt="" src="../images/down.gif" /> <a href="#user">User</a></li>
</ul>
</div>

<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="AcceptMutex" id="AcceptMutex">AcceptMutex</a> <a name="acceptmutex" id="acceptmutex">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Method that Apache uses to serialize multiple children
accepting requests on network sockets</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>AcceptMutex Default|<var>method</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>AcceptMutex Default</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The <code class="directive">AcceptMutex</code> directives sets the
    method that Apache uses to serialize multiple children accepting
    requests on network sockets. Prior to Apache 2.0, the method was
    selectable only at compile time. The optimal method to use is
    highly architecture and platform dependent. For further details,
    see the <a href="../misc/perf-tuning.html">performance tuning</a>
    documentation.</p>

    <p>If this directive is set to <code>Default</code>, then the
    compile-time selected default will be used. Other possible
    methods are listed below. Note that not all methods are
    available on all platforms. If a method is specified which is
    not available, a message will be written to the error log
    listing the available methods.</p>

    <dl>
      <dt><code>flock</code></dt>
      <dd>uses the <code>flock(2)</code> system call to lock the
      file defined by the <code class="directive"><a href="#lockfile">LockFile</a></code> directive.</dd>

      <dt><code>fcntl</code></dt>
      <dd>uses the <code>fcntl(2)</code> system call to lock the
      file defined by the <code class="directive"><a href="#lockfile">LockFile</a></code> directive.</dd>

      <dt><code>posixsem</code></dt>
      <dd>uses POSIX compatible semaphores to implement the mutex.</dd>

      <dt><code>pthread</code></dt>
      <dd>uses POSIX mutexes as implemented by the POSIX Threads
      (PThreads) specification.</dd>

      <dt><code>sysvsem</code></dt>
      <dd>uses SySV-style semaphores to implement the mutex.</dd>
    </dl>

    <p>If you want to find out the compile time chosen default
    for your system, you may set your <code class="directive"><a href="../mod/core.html#loglevel">LogLevel</a></code> to <code>debug</code>. Then the default <code class="directive">AcceptMutex</code> will be written into the <code class="directive"><a href="../mod/core.html#errorlog">ErrorLog</a></code>.</p>

  <div class="warning"><h3>Warning</h3>
     <p>On most systems, when the <code>pthread</code> option
     is selected, if a child process terminates abnormally
     while holding the <code>AcceptCntl</code> mutex the
     server will stop responding to requests. When this
     occurs, the server will require a manual restart to
     recover.</p>
     <p>Solaris is a notable exception as it provides a
     mechanism, used by Apache, which usually allows the
     mutex to be recovered after a child process terminates
     abnormally while holding a mutex.</p>
     <p>If your system implements the
     <code>pthread_mutexattr_setrobust_np()</code> function,
     you may be able to use the <code>pthread</code> option safely.</p>
  </div>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="CoreDumpDirectory" id="CoreDumpDirectory">CoreDumpDirectory</a> <a name="coredumpdirectory" id="coredumpdirectory">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Directory where Apache attempts to
switch before dumping core</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>CoreDumpDirectory <var>directory</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for the default setting</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>This controls the directory to which Apache attempts to
    switch before dumping core. The default is in the
    <code class="directive"><a href="../mod/core.html#serverroot">ServerRoot</a></code> directory, however
    since this should not be writable by the user the server runs
    as, core dumps won't normally get written. If you want a core
    dump for debugging, you can use this directive to place it in a
    different location.</p>

    <div class="note"><h3>Core Dumps on Linux</h3>
      <p>If Apache starts as root and switches to another user, the
      Linux kernel <em>disables</em> core dumps even if the directory is
      writable for the process. Apache (2.0.46 and later) reenables core dumps
      on Linux 2.4 and beyond, but only if you explicitly configure a <code class="directive">CoreDumpDirectory</code>.</p>
    </div>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="EnableExceptionHook" id="EnableExceptionHook">EnableExceptionHook</a> <a name="enableexceptionhook" id="enableexceptionhook">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Enables a hook that runs exception handlers
after a crash</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>EnableExceptionHook On|Off</code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>EnableExceptionHook Off</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
<tr><th><a href="directive-dict.html#Compatibility">Compatibility:</a></th><td>Available in version 2.0.49 and later</td></tr>
</table>
    <p>For safety reasons this directive is only available if the server was
    configured with the <code>--enable-exception-hook</code> option. It
    enables a hook that allows external modules to plug in and do something
    after a child crashed.</p>
    
    <p>There are already two modules, <code>mod_whatkilledus</code> and
    <code>mod_backtrace</code> that make use of this hook. Please have a
    look at Jeff Trawick's <a href="http://www.apache.org/~trawick/exception_hook.html">EnableExceptionHook site</a> for more information about these.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="GracefulShutdownTimeout" id="GracefulShutdownTimeout">GracefulShutdownTimeout</a> <a name="gracefulshutdowntimeout" id="gracefulshutdowntimeout">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Specify a timeout after which a gracefully shutdown server
will exit.</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>GracefulShutDownTimeout <var>seconds</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>GracefulShutDownTimeout 0</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code>, <code class="module"><a href="../mod/event.html">event</a></code></td></tr>
<tr><th><a href="directive-dict.html#Compatibility">Compatibility:</a></th><td>Available in version 2.2 and later</td></tr>
</table>
    <p>The <code class="directive">GracefulShutdownTimeout</code> specifies
    how many seconds after receiving a "graceful-stop" signal, a 
    server should continue to run, handling the existing connections.</p>

    <p>Setting this value to zero means that the server will wait
    indefinitely until all remaining requests have been fully served.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="Group" id="Group">Group</a> <a name="group" id="group">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Group under which the server will answer
requests</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>Group <var>unix-group</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>Group #-1</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
<tr><th><a href="directive-dict.html#Compatibility">Compatibility:</a></th><td>Only valid in global server config since Apache
2.0</td></tr>
</table>
    <p>The <code class="directive">Group</code> directive sets the group under
    which the server will answer requests. In order to use this
    directive, the server must be run initially as <code>root</code>. If
    you start the server as a non-root user, it will fail to change to the
    specified group, and will instead continue to run as the group of the
    original user. <var>Unix-group</var> is one of:</p>

    <dl>
      <dt>A group name</dt>
      <dd>Refers to the given group by name.</dd>

      <dt><code>#</code> followed by a group number.</dt>
      <dd>Refers to a group by its number.</dd>
    </dl>

    <div class="example"><h3>Example</h3><p><code>
      Group www-group
    </code></p></div>

    <p>It is recommended that you set up a new group specifically for
    running the server. Some admins use user <code>nobody</code>,
    but this is not always possible or desirable.</p>

    <div class="warning"><h3>Security</h3>
      <p>Don't set <code class="directive">Group</code> (or <code class="directive"><a href="#user">User</a></code>) to <code>root</code> unless
      you know exactly what you are doing, and what the dangers are.</p>
    </div>

    <p>Special note: Use of this directive in <code class="directive"><a href="../mod/core.html#virtualhost">&lt;VirtualHost&gt;</a></code> is no longer supported. To
    configure your server for <code class="program"><a href="../programs/suexec.html">suexec</a></code> use
    <code class="directive"><a href="../mod/mod_suexec.html#suexecusergroup">SuexecUserGroup</a></code>.</p>

    <div class="note"><h3>Note</h3>
      <p>Although the <code class="directive">Group</code> directive is present
      in the <code class="module"><a href="../mod/beos.html">beos</a></code> and <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code> MPMs,
      it is actually a no-op there and only exists for compatibility
      reasons.</p>
    </div>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="Listen" id="Listen">Listen</a> <a name="listen" id="listen">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>IP addresses and ports that the server
listens to</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>Listen [<var>IP-address</var>:]<var>portnumber</var> [<var>protocol</var>]</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code>, <code class="module"><a href="../mod/event.html">event</a></code></td></tr>
<tr><th><a href="directive-dict.html#Compatibility">Compatibility:</a></th><td>Required directive since Apache 2.0<br />
The <var>protocol</var> argument was added in 2.1.5</td></tr>
</table>
    <p>The <code class="directive">Listen</code> directive instructs Apache to
    listen to only specific IP addresses or ports; by default it
    responds to requests on all IP interfaces. <code class="directive">Listen</code>
    is now a required directive. If it is not in the config file, the
    server will fail to start. This is a change from previous versions
    of Apache.</p>

    <p>The <code class="directive">Listen</code> directive tells the server to
    accept incoming requests on the specified port or address-and-port
    combination. If only a port number is specified, the server listens to
    the given port on all interfaces. If an IP address is given as well
    as a port, the server will listen on the given port and
    interface.</p>

    <p>Multiple <code class="directive">Listen</code> directives may be used to
    specify a number of addresses and ports to listen to. The server will
    respond to requests from any of the listed addresses and ports.</p>

    <p>For example, to make the server accept connections on both
    port 80 and port 8000, use:</p>

    <div class="example"><p><code>
      Listen 80<br />
      Listen 8000
    </code></p></div>

    <p>To make the server accept connections on two specified
    interfaces and port numbers, use </p>

    <div class="example"><p><code>
      Listen ***********:80<br />
      Listen ***********:8000
    </code></p></div>

    <p>IPv6 addresses must be surrounded in square brackets, as in the
    following example:</p>

    <div class="example"><p><code>
      Listen [2001:db8::a00:20ff:fea7:ccea]:80
    </code></p></div>

    <p>The optional <var>protocol</var> argument is not required for most 
       configurations. If not specified, <code>https</code> is the default for 
       port 443 and <code>http</code> the default for all other ports.  The 
       protocol is used to determine which module should handle a request, and
       to apply protocol specific optimizations with the 
       <code class="directive"><a href="../mod/core.html#acceptfilter">AcceptFilter</a></code> directive.</p>

    <p>You only need to set the protocol if you are running on non-standard 
       ports.  For example, running an <code>https</code> site on port 8443:</p>

    <div class="example"><p><code>
      Listen ***********:8443 https
    </code></p></div>

    <div class="note"><h3>Error condition</h3>
      Multiple <code class="directive">Listen</code> directives for the same ip
      address and port will result in an <code>Address already in use</code>
      error message.
    </div>


<h3>See also</h3>
<ul>
<li><a href="../dns-caveats.html">DNS Issues</a></li>
<li><a href="../bind.html">Setting which addresses and ports Apache
    uses</a></li>
</ul>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="ListenBackLog" id="ListenBackLog">ListenBackLog</a> <a name="listenbacklog" id="listenbacklog">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Maximum length of the queue of pending connections</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>ListenBacklog <var>backlog</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>ListenBacklog 511</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The maximum length of the queue of pending connections.
    Generally no tuning is needed or desired, however on some
    systems it is desirable to increase this when under a TCP SYN
    flood attack. See the backlog parameter to the
    <code>listen(2)</code> system call.</p>

    <p>This will often be limited to a smaller number by the
    operating system. This varies from OS to OS. Also note that
    many OSes do not use exactly what is specified as the backlog,
    but use a number based on (but normally larger than) what is
    set.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="LockFile" id="LockFile">LockFile</a> <a name="lockfile" id="lockfile">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Location of the accept serialization lock file</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>LockFile <var>filename</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>LockFile logs/accept.lock</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The <code class="directive">LockFile</code> directive sets the path to
    the lockfile used when Apache is used with an <code class="directive"><a href="#acceptmutex">AcceptMutex</a></code> value of either
    <code>fcntl</code> or <code>flock</code>. This directive should
    normally be left at its default value. The main reason for changing
    it is if the <code>logs</code> directory is NFS mounted, since
    <strong>the lockfile must be stored on a local disk</strong>. The PID
    of the main server process is automatically appended to the
    filename.</p>

    <div class="warning"><h3>Security</h3>
      <p>It is best to <em>avoid</em> putting this file in a world writable
      directory such as <code>/var/tmp</code> because someone could create
      a denial of service attack and prevent the server from starting by
      creating a lockfile with the same name as the one the server will try
      to create.</p>
    </div>

<h3>See also</h3>
<ul>
<li><code class="directive"><a href="#acceptmutex">AcceptMutex</a></code></li>
</ul>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="MaxClients" id="MaxClients">MaxClients</a> <a name="maxclients" id="maxclients">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Maximum number of child processes that will be created
to serve requests</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>MaxClients <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The <code class="directive">MaxClients</code> directive sets the limit
    on the number of simultaneous requests that will be served.  Any
    connection attempts over the <code class="directive">MaxClients</code>
    limit will normally be queued, up to a number based on the
    <code class="directive"><a href="#listenbacklog">ListenBacklog</a></code>
    directive. Once a child process is freed at the end of a different
    request, the connection will then be serviced.</p>

    <p>For non-threaded servers (<em>i.e.</em>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>),
    <code class="directive">MaxClients</code> translates into the maximum
    number of child processes that will be launched to serve requests.
    The default value is <code>256</code>; to increase it, you must also raise
    <code class="directive"><a href="#serverlimit">ServerLimit</a></code>.</p>

    <p>For threaded and hybrid servers (<em>e.g.</em> <code class="module"><a href="../mod/beos.html">beos</a></code>
    or <code class="module"><a href="../mod/worker.html">worker</a></code>) <code class="directive">MaxClients</code> restricts
    the total number of threads that will be available to serve clients.
    The default value for <code class="module"><a href="../mod/beos.html">beos</a></code> is <code>50</code>. For
    hybrid MPMs the default value is <code>16</code> (<code class="directive"><a href="#serverlimit">ServerLimit</a></code>) multiplied by the value of
    <code>25</code> (<code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code>). Therefore, to increase <code class="directive">MaxClients</code> to a value that requires more than 16 processes,
    you must also raise <code class="directive"><a href="#serverlimit">ServerLimit</a></code>.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="MaxMemFree" id="MaxMemFree">MaxMemFree</a> <a name="maxmemfree" id="maxmemfree">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Maximum amount of memory that the main allocator is allowed
to hold without calling <code>free()</code></td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>MaxMemFree <var>KBytes</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>MaxMemFree 0</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code></td></tr>
</table>
    <p>The <code class="directive">MaxMemFree</code> directive sets the
    maximum number of free Kbytes that the main allocator is allowed
    to hold without calling <code>free()</code>. When not set, or when set
    to zero, the threshold will be set to unlimited.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="MaxRequestsPerChild" id="MaxRequestsPerChild">MaxRequestsPerChild</a> <a name="maxrequestsperchild" id="maxrequestsperchild">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Limit on the number of requests that an individual child server
will handle during its life</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>MaxRequestsPerChild <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>MaxRequestsPerChild 10000</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The <code class="directive">MaxRequestsPerChild</code> directive sets
    the limit on the number of requests that an individual child
    server process will handle. After
    <code class="directive">MaxRequestsPerChild</code> requests, the child
    process will die. If <code class="directive">MaxRequestsPerChild</code> is
    <code>0</code>, then the process will never expire.</p>

    <div class="note"><h3>Different default values</h3>
      <p>The default value for <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code> and
      <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code> is <code>0</code>.</p>
    </div>

    <p>Setting <code class="directive">MaxRequestsPerChild</code> to a
    non-zero limit has two beneficial effects:</p>

    <ul>
      <li>it limits the amount of memory that process can consume
      by (accidental) memory leakage;</li>

      <li>by giving processes a finite lifetime, it helps reduce
      the number of processes when the server load reduces.</li>
    </ul>

    <div class="note"><h3>Note</h3>
      <p>For <code class="directive"><a href="../mod/core.html#keepalive">KeepAlive</a></code> requests, only
      the first request is counted towards this limit. In effect, it
      changes the behavior to limit the number of <em>connections</em> per
      child.</p>
    </div>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="MaxSpareThreads" id="MaxSpareThreads">MaxSpareThreads</a> <a name="maxsparethreads" id="maxsparethreads">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Maximum number of idle threads</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>MaxSpareThreads <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>Maximum number of idle threads. Different MPMs deal with this
    directive differently.</p>

    <p>For <code class="module"><a href="../mod/worker.html">worker</a></code>,
    the default is <code>MaxSpareThreads 250</code>.
    These MPMs deal with idle threads on a server-wide basis. If there
    are too many idle threads in the server then child processes are
    killed until the number of idle threads is less than this number.</p>

    <p>For <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code> the default is
    <code>MaxSpareThreads 100</code>. Since this MPM runs a
    single-process, the spare thread count is also server-wide.</p>

    <p><code class="module"><a href="../mod/beos.html">beos</a></code> and <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code> work
    similar to <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>. The default for
    <code class="module"><a href="../mod/beos.html">beos</a></code> is <code>MaxSpareThreads 50</code>. For
    <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code> the default value is <code>10</code>.</p>

    <div class="note"><h3>Restrictions</h3>
      <p>The range of the <code class="directive">MaxSpareThreads</code> value
      is restricted. Apache will correct the given value automatically
      according to the following rules:</p>
      <ul>

        <li><code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code> wants the value to be greater than
        <code class="directive"><a href="#minsparethreads">MinSpareThreads</a></code>.</li>

        <li>For
        <code class="module"><a href="../mod/worker.html">worker</a></code> the value must be greater or equal than
        the sum of <code class="directive"><a href="#minsparethreads">MinSpareThreads</a></code>
        and <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code>.</li>
      </ul>
    </div>

<h3>See also</h3>
<ul>
<li><code class="directive"><a href="#minsparethreads">MinSpareThreads</a></code></li>
<li><code class="directive"><a href="#startservers">StartServers</a></code></li>
</ul>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="MinSpareThreads" id="MinSpareThreads">MinSpareThreads</a> <a name="minsparethreads" id="minsparethreads">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Minimum number of idle threads available to handle request
spikes</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>MinSpareThreads <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>Minimum number of idle threads to handle request spikes.
    Different MPMs deal with this directive
    differently.</p>


    <p><code class="module"><a href="../mod/worker.html">worker</a></code>
     uses a default of <code>MinSpareThreads
    75</code> and deal with idle threads on a server-wide basis. If
    there aren't enough idle threads in the server then child
    processes are created until the number of idle threads is greater
    than number.</p>

    <p><code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code> uses a default of
    <code>MinSpareThreads 10</code> and, since it is a single-process
    MPM, tracks this on a server-wide bases.</p>

    <p><code class="module"><a href="../mod/beos.html">beos</a></code> and <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code> work
    similar to <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>. The default for
    <code class="module"><a href="../mod/beos.html">beos</a></code> is <code>MinSpareThreads 1</code>. For
    <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code> the default value is <code>5</code>.</p>

<h3>See also</h3>
<ul>
<li><code class="directive"><a href="#maxsparethreads">MaxSpareThreads</a></code></li>
<li><code class="directive"><a href="#startservers">StartServers</a></code></li>
</ul>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="PidFile" id="PidFile">PidFile</a> <a name="pidfile" id="pidfile">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>File where the server records the process ID
of the daemon</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>PidFile <var>filename</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>PidFile logs/httpd.pid</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The <code class="directive">PidFile</code> directive sets the file to
    which the server records the process id of the daemon. If the
    filename is not absolute then it is assumed to be relative to the
    <code class="directive"><a href="../mod/core.html#serverroot">ServerRoot</a></code>.</p>

    <div class="example"><h3>Example</h3><p><code>
      PidFile /var/run/apache.pid
    </code></p></div>

    <p>It is often useful to be able to send the server a signal,
    so that it closes and then re-opens its <code class="directive"><a href="../mod/core.html#errorlog">ErrorLog</a></code> and <code class="directive"><a href="../mod/mod_log_config.html#transferlog">TransferLog</a></code>, and
    re-reads its configuration files. This is done by sending a
    SIGHUP (kill -1) signal to the process id listed in the
    <code class="directive">PidFile</code>.</p>

    <p>The <code class="directive">PidFile</code> is subject to the same
    warnings about log file placement and <a href="../misc/security_tips.html#serverroot">security</a>.</p>

    <div class="note"><h3>Note</h3>
      <p>As of Apache 2 it is recommended to use only the <code class="program"><a href="../programs/apachectl.html">apachectl</a></code> script for (re-)starting or stopping the server.</p>
    </div>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="ReceiveBufferSize" id="ReceiveBufferSize">ReceiveBufferSize</a> <a name="receivebuffersize" id="receivebuffersize">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>TCP receive buffer size</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>ReceiveBufferSize <var>bytes</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>ReceiveBufferSize 0</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The server will set the TCP receive buffer size to the number of
    bytes specified.</p>

    <p>If set to the value of <code>0</code>, the server will use the
    OS default.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="ScoreBoardFile" id="ScoreBoardFile">ScoreBoardFile</a> <a name="scoreboardfile" id="scoreboardfile">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Location of the file used to store coordination data for
the child processes</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>ScoreBoardFile <var>file-path</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>ScoreBoardFile logs/apache_status</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>Apache uses a scoreboard to communicate between its parent
    and child processes.  Some architectures require a file to facilitate
    this communication. If the file is left unspecified, Apache first
    attempts to create the scoreboard entirely in memory (using anonymous
    shared memory) and, failing that, will attempt to create the file on
    disk (using file-based shared memory). Specifying this directive causes
    Apache to always create the file on the disk.</p>

    <div class="example"><h3>Example</h3><p><code>
      ScoreBoardFile /var/run/apache_status
    </code></p></div>

    <p>File-based shared memory is useful for third-party applications
    that require direct access to the scoreboard.</p>

    <p>If you use a <code class="directive">ScoreBoardFile</code> then
    you may see improved speed by placing it on a RAM disk. But be
    careful that you heed the same warnings about log file placement
    and <a href="../misc/security_tips.html">security</a>.</p>

<h3>See also</h3>
<ul>
<li><a href="../stopping.html">Stopping and Restarting
Apache</a></li>
</ul>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="SendBufferSize" id="SendBufferSize">SendBufferSize</a> <a name="sendbuffersize" id="sendbuffersize">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>TCP buffer size</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>SendBufferSize <var>bytes</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>SendBufferSize 0</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The server will set the TCP send buffer size to the number of bytes
    specified. Very useful to increase past standard OS defaults on
    high speed high latency (<em>i.e.</em>, 100ms or so, such as
    transcontinental fast pipes).</p>

    <p>If set to the value of <code>0</code>, the server will use the
    OS default.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="ServerLimit" id="ServerLimit">ServerLimit</a> <a name="serverlimit" id="serverlimit">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Upper limit on configurable number of processes</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>ServerLimit <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>For the <code class="module"><a href="../mod/prefork.html">prefork</a></code> MPM, this directive sets the
    maximum configured value for <code class="directive"><a href="#maxclients">MaxClients</a></code> for the lifetime of the
    Apache process.  For the <code class="module"><a href="../mod/worker.html">worker</a></code> MPM, this directive
    in combination with <code class="directive"><a href="#threadlimit">ThreadLimit</a></code> sets
    the maximum configured value for <code class="directive"><a href="#maxclients">MaxClients</a></code> for the lifetime of the
    Apache process.  Any attempts to change this directive during a
    restart will be ignored, but <code class="directive"><a href="#maxclients">MaxClients</a></code> can be modified during
    a restart.</p>

    <p>Special care must be taken when using this directive.  If
    <code class="directive">ServerLimit</code> is set to a value much higher
    than necessary, extra, unused shared memory will be allocated.  If
    both <code class="directive">ServerLimit</code> and <code class="directive"><a href="#maxclients">MaxClients</a></code> are set to values
    higher than the system can handle, Apache may not start or the
    system may become unstable.</p>

    <p>With the <code class="module"><a href="../mod/prefork.html">prefork</a></code> MPM, use this directive only
    if you need to set <code class="directive"><a href="#maxclients">MaxClients</a></code> higher than 256 (default).
    Do not set the value of this directive any higher than what you
    might want to set <code class="directive"><a href="#maxclients">MaxClients</a></code> to.</p>

    <p>With <code class="module"><a href="../mod/worker.html">worker</a></code>
     use this directive only
    if your <code class="directive"><a href="#maxclients">MaxClients</a></code> and
    <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code>
    settings require more than 16 server processes (default). Do not set
    the value of this directive any higher than the number of server
    processes required by what you may want for <code class="directive"><a href="#maxclients ">MaxClients </a></code> and <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code>.</p>

    <div class="note"><h3>Note</h3>
      <p>There is a hard limit of <code>ServerLimit 20000</code> compiled
      into the server (for the <code class="module"><a href="../mod/prefork.html">prefork</a></code> MPM 200000). This is
      intended to avoid nasty effects caused by typos.</p>
    </div>

<h3>See also</h3>
<ul>
<li><a href="../stopping.html">Stopping and Restarting Apache</a></li>
</ul>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="StartServers" id="StartServers">StartServers</a> <a name="startservers" id="startservers">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Number of child server processes created at startup</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>StartServers <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code>, <code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>The <code class="directive">StartServers</code> directive sets the
    number of child server processes created on startup. As the number
    of processes is dynamically controlled depending on the load,
    there is usually little reason to adjust this parameter.</p>

    <p>The default value differs from MPM to MPM. For
    <code class="module"><a href="../mod/worker.html">worker</a></code> the default is <code>StartServers 3</code>.
    For <code class="module"><a href="../mod/prefork.html">prefork</a></code> defaults to <code>5</code> and for
    <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code> to <code>2</code>.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="StartThreads" id="StartThreads">StartThreads</a> <a name="startthreads" id="startthreads">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Number of threads created on startup</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>StartThreads <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/beos.html">beos</a></code>, <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code></td></tr>
</table>
    <p>Number of threads created on startup. As the
    number of threads is dynamically controlled depending on the
    load, there is usually little reason to adjust this
    parameter.</p>

    <p>For <code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code> the default is
    <code>StartThreads 50</code> and, since there is only a single
    process, this is the total number of threads created at startup to
    serve requests.</p>

    <p>For <code class="module"><a href="../mod/beos.html">beos</a></code> the default is <code>StartThreads
    10</code>. It also reflects the total number of threads created
    at startup to serve requests.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="ThreadLimit" id="ThreadLimit">ThreadLimit</a> <a name="threadlimit" id="threadlimit">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Sets the upper limit on the configurable number of threads
per child process</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>ThreadLimit <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
<tr><th><a href="directive-dict.html#Compatibility">Compatibility:</a></th><td>Available for <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code> in Apache 2.0.41
and later</td></tr>
</table>
    <p>This directive sets the maximum configured value for <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code> for the lifetime
    of the Apache process.  Any attempts to change this directive
    during a restart will be ignored, but <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code> can be modified
    during a restart up to the value of this directive.</p>

    <p>Special care must be taken when using this directive.  If
    <code class="directive">ThreadLimit</code> is set to a value much higher
    than <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code>,
    extra unused shared memory will be allocated.  If both
    <code class="directive">ThreadLimit</code> and <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code> are set to values
    higher than the system can handle, Apache may not start or the
    system may become unstable. Do not set the value of this directive
    any higher than your greatest predicted setting of <code class="directive"><a href="#threadsperchild">ThreadsPerChild</a></code> for the
    current run of Apache.</p>

    <p>The default value for <code class="directive">ThreadLimit</code> is
    <code>1920</code> when used with <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code> and
    <code>64</code> when used with the others.</p>

    <div class="note"><h3>Note</h3>
      <p>There is a hard limit of <code>ThreadLimit 20000</code> (or
      <code>ThreadLimit 15000</code> with <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>)
      compiled into the server. This is intended to avoid nasty effects
      caused by typos.</p>
    </div>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="ThreadsPerChild" id="ThreadsPerChild">ThreadsPerChild</a> <a name="threadsperchild" id="threadsperchild">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>Number of threads created by each child process</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>ThreadsPerChild <var>number</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>See usage for details</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
</table>
    <p>This directive sets the number of threads created by each
    child process. The child creates these threads at startup and
    never creates more. If using an MPM like <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>,
    where there is only one child process, this number should be high
    enough to handle the entire load of the server. If using an MPM
    like <code class="module"><a href="../mod/worker.html">worker</a></code>, where there are multiple child processes,
    the <em>total</em> number of threads should be high enough to handle
    the common load on the server.</p>

    <p>The default value for <code class="directive">ThreadsPerChild</code> is
    <code>64</code> when used with <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code> and
    <code>25</code> when used with the others.</p>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="ThreadStackSize" id="ThreadStackSize">ThreadStackSize</a> <a name="threadstacksize" id="threadstacksize">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>The size in bytes of the stack used by threads handling 
client connections</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>ThreadStackSize <var>size</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>65536 on NetWare; varies on other operating systems</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/mpm_netware.html">mpm_netware</a></code>, <code class="module"><a href="../mod/mpm_winnt.html">mpm_winnt</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
<tr><th><a href="directive-dict.html#Compatibility">Compatibility:</a></th><td>Available in Apache 2.1 and later</td></tr>
</table>
    <p>The <code class="directive">ThreadStackSize</code> directive sets the 
    size of the stack (for autodata) of threads which handle client
    connections and call modules to help process those connections.  
    In most cases the operating system default for stack size is 
    reasonable, but there are some conditions where it may need to be 
    adjusted:</p>

    <ul>
      <li>On platforms with a relatively small default thread stack size
      (e.g., HP-UX), Apache may crash when using some third-party modules
      which use a relatively large amount of autodata storage.  Those
      same modules may have worked fine on other platforms where the
      default thread stack size is larger.  This type of crash is
      resolved by setting <code class="directive">ThreadStackSize</code> to a 
      value higher than the operating system default.  This type of 
      adjustment is necessary only if the provider of the third-party 
      module specifies that it is required, or if diagnosis of an Apache 
      crash indicates that the thread stack size was too small.</li>

      <li>On platforms where the default thread stack size is 
      significantly larger than necessary for the web server
      configuration, a higher number of threads per child process
      will be achievable if <code class="directive">ThreadStackSize</code> is
      set to a value lower than the operating system default.  This type
      of adjustment should only be made in a test environment which allows
      the full set of web server processing can be exercised, as there
      may be infrequent requests which require more stack to process.
      A change in the web server configuration can invalidate the
      current <code class="directive">ThreadStackSize</code> setting.</li>
    </ul>

</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="directive-section"><h2><a name="User" id="User">User</a> <a name="user" id="user">Directive</a></h2>
<table class="directive">
<tr><th><a href="directive-dict.html#Description">Description:</a></th><td>The userid under which the server will answer
requests</td></tr>
<tr><th><a href="directive-dict.html#Syntax">Syntax:</a></th><td><code>User <var>unix-userid</var></code></td></tr>
<tr><th><a href="directive-dict.html#Default">Default:</a></th><td><code>User #-1</code></td></tr>
<tr><th><a href="directive-dict.html#Context">Context:</a></th><td>server config</td></tr>
<tr><th><a href="directive-dict.html#Status">Status:</a></th><td>MPM</td></tr>
<tr><th><a href="directive-dict.html#Module">Module:</a></th><td><code class="module"><a href="../mod/prefork.html">prefork</a></code>, <code class="module"><a href="../mod/worker.html">worker</a></code></td></tr>
<tr><th><a href="directive-dict.html#Compatibility">Compatibility:</a></th><td>Only valid in global server config since Apache
2.0</td></tr>
</table>
    <p>The <code class="directive">User</code> directive sets the user ID as
    which the server will answer requests. In order to use this
    directive, the server must be run initially as <code>root</code>.
    If you start the server as a non-root user, it will fail to change
    to the lesser privileged user, and will instead continue to run as
    that original user. If you do start the server as <code>root</code>,
    then it is normal for the parent process to remain running as root.
    <var>Unix-userid</var> is one of:</p>

    <dl>
      <dt>A username</dt>
      <dd>Refers to the given user by name.</dd>

      <dt># followed by a user number.</dt>
      <dd>Refers to a user by its number.</dd>
    </dl>

    <p>The user should have no privileges that result in it being
    able to access files that are not intended to be visible to the
    outside world, and similarly, the user should not be able to
    execute code that is not meant for HTTP requests. It is
    recommended that you set up a new user and group specifically for
    running the server. Some admins use user <code>nobody</code>, but
    this is not always desirable, since the <code>nobody</code> user
    can have other uses on the system.</p>

    <div class="warning"><h3>Security</h3>
      <p>Don't set <code class="directive">User</code> (or <code class="directive"><a href="#group">Group</a></code>) to <code>root</code> unless
      you know exactly what you are doing, and what the dangers are.</p>
    </div>

    <p>Special note: Use of this directive in <code class="directive"><a href="../mod/core.html#virtualhost">&lt;VirtualHost&gt;</a></code> is no longer supported. To
    configure your server for <code class="program"><a href="../programs/suexec.html">suexec</a></code> use
    <code class="directive"><a href="../mod/mod_suexec.html#suexecusergroup">SuexecUserGroup</a></code>.</p>

    <div class="note"><h3>Note</h3>
      <p>Although the <code class="directive">User</code> directive is present
      in the <code class="module"><a href="../mod/beos.html">beos</a></code> and <code class="module"><a href="../mod/mpmt_os2.html">mpmt_os2</a></code> MPMs,
      it is actually a no-op there and only exists for compatibility
      reasons.</p>
    </div>

</div>
</div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../de/mod/mpm_common.html" hreflang="de" rel="alternate" title="Deutsch">&nbsp;de&nbsp;</a> |
<a href="../en/mod/mpm_common.html" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/mod/mpm_common.html" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>