<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head><!--
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
              This file is generated from xml source: DO NOT EDIT
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      -->
<title>How-To / Tutorials - Apache HTTP Server</title>
<link href="../style/css/manual.css" rel="stylesheet" media="all" type="text/css" title="Main stylesheet" />
<link href="../style/css/manual-loose-100pc.css" rel="alternate stylesheet" media="all" type="text/css" title="No Sidebar - Default font size" />
<link href="../style/css/manual-print.css" rel="stylesheet" media="print" type="text/css" />
<link href="../images/favicon.ico" rel="shortcut icon" /></head>
<body id="manual-page" class="no-sidebar"><div id="page-header">
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p>
<p class="apache">Apache HTTP Server Version 2.2</p>
<img alt="" src="../images/feather.gif" /></div>
<div class="up"><a href="../"><img title="&lt;-" alt="&lt;-" src="../images/left.gif" /></a></div>
<div id="path">
<a href="http://www.apache.org/">Apache</a> &gt; <a href="http://httpd.apache.org/">HTTP Server</a> &gt; <a href="http://httpd.apache.org/docs/">Documentation</a> &gt; <a href="../">Version 2.2</a></div><div id="page-content"><div id="preamble"><h1>How-To / Tutorials</h1>
<div class="toplang">
<p><span>Available Languages: </span><a href="../en/howto/" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/howto/" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/howto/" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div>
</div>
<div class="top"><a href="#page-header"><img alt="top" src="../images/up.gif" /></a></div>
<div class="section">
<h2><a name="howto" id="howto">How-To / Tutorials</a></h2>

    

    <dl>
      <dt>Authentication and Authorization</dt>
      <dd>
        <p>Authentication is any process by which you verify that
        someone is who they claim they are. Authorization is any
        process by which someone is allowed to be where they want to
        go, or to have information that they want to have.</p>

        <p>See: <a href="auth.html">Authentication, Authorization</a></p>
      </dd>
    </dl>

    <dl>
      <dt>Access Control</dt>
      <dd>
        <p>Access control refers to the process of restricting, or
        granting access to a resource based on arbitrary criteria. There
        are a variety of different ways that this can be
        accomplished.</p>

        <p>See: <a href="access.html">Access Control</a></p>
      </dd>
    </dl>

   <dl>
      <dt>Dynamic Content with CGI</dt>
      <dd>
        <p>The CGI (Common Gateway Interface) defines a way for a web
        server to interact with external content-generating programs,
        which are often referred to as CGI programs or CGI scripts. It
        is the simplest, and most common, way to put dynamic content on
        your web site. This document will be an introduction to setting
        up CGI on your Apache web server, and getting started writing
        CGI programs.</p>

        <p>See: <a href="cgi.html">CGI: Dynamic Content</a></p>
      </dd>
    </dl>

    <dl>
      <dt><code>.htaccess</code> files</dt>
      <dd>
        <p><code>.htaccess</code> files provide a way to make configuration
        changes on a per-directory basis. A file, containing one or more
        configuration directives, is placed in a particular document directory,
        and the directives apply to that directory, and all subdirectories thereof.</p>

        <p>See: <a href="htaccess.html"><code>.htaccess</code> files</a></p>
      </dd>
    </dl>

    <dl>
      <dt>Introduction to Server Side Includes</dt>
      <dd>
        <p>SSI (Server Side Includes) are directives that are placed in
        HTML pages, and evaluated on the server while the pages are
        being served. They let you add dynamically generated content to
        an existing HTML page, without having to serve the entire page
        via a CGI program, or other dynamic technology.</p>

        <p>See: <a href="ssi.html">Server Side Includes (SSI)</a></p>
      </dd>
    </dl>

    <dl>
      <dt>Per-user web directories</dt>
      <dd>
        <p>On systems with multiple users, each user can be permitted to have a
        web site in their home directory using the <code class="directive"><a href="../mod/mod_userdir.html#userdir">UserDir</a></code> directive. Visitors
        to a URL <code>http://example.com/~username/</code> will get content
        out of the home directory of the user "<code>username</code>", out of
        the subdirectory specified by the <code class="directive"><a href="../mod/mod_userdir.html#userdir">UserDir</a></code> directive.</p>

        <p>See: <a href="public_html.html">User web directories (<code>public_html</code>)</a></p>
      </dd>
    </dl>

  </div></div>
<div class="bottomlang">
<p><span>Available Languages: </span><a href="../en/howto/" title="English">&nbsp;en&nbsp;</a> |
<a href="../ja/howto/" hreflang="ja" rel="alternate" title="Japanese">&nbsp;ja&nbsp;</a> |
<a href="../ko/howto/" hreflang="ko" rel="alternate" title="Korean">&nbsp;ko&nbsp;</a></p>
</div><div id="footer">
<p class="apache">Copyright 2006 The Apache Software Foundation.<br />Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>
<p class="menu"><a href="../mod/">Modules</a> | <a href="../mod/directives.html">Directives</a> | <a href="../faq/">FAQ</a> | <a href="../glossary.html">Glossary</a> | <a href="../sitemap.html">Sitemap</a></p></div>
</body></html>