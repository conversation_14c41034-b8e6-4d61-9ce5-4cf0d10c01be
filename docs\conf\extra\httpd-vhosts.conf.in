#
# Virtual Hosts
#
# If you want to maintain multiple domains/hostnames on your
# machine you can setup VirtualHost containers for them. Most configurations
# use only name-based virtual hosts so the server doesn't need to worry about
# IP addresses. This is indicated by the asterisks in the directives below.
#
# Please see the documentation at 
# <URL:http://httpd.apache.org/docs/2.2/vhosts/>
# for further details before you try to setup virtual hosts.
#
# You may use the command line option '-S' to verify your virtual host
# configuration.

#
# Use name-based virtual hosting.
#
NameVirtualHost *:80

#
# VirtualHost example:
# Almost any Apache directive may go into a VirtualHost container.
# The first VirtualHost section is used for all requests that do not
# match a ServerName or ServerAlias in any <VirtualHost> block.
#
<VirtualHost *:80>
    ServerAdmin <EMAIL>
    DocumentRoot /www/docs/dummy-host.example.com
    ServerName dummy-host.example.com
    ServerAlias www.dummy-host.example.com
    ErrorLog @rel_logfiledir@/dummy-host.example.com-error_log
    CustomLog @rel_logfiledir@/dummy-host.example.com-access_log common
</VirtualHost>

<VirtualHost *:80>
    ServerAdmin <EMAIL>
    DocumentRoot /www/docs/dummy-host2.example.com
    ServerName dummy-host2.example.com
    ErrorLog @rel_logfiledir@/dummy-host2.example.com-error_log
    CustomLog @rel_logfiledir@/dummy-host2.example.com-access_log common
</VirtualHost>



